'use client';

import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Plus,
  Search,
  MoreVertical,
  Activity,
  Package,
  DollarSign,
  Trash,
  Pencil,
  ArrowLeft,
  Download,
  Printer,
} from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend } from 'recharts';
import { format } from 'date-fns';

const DrugPurchasesPage = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { language, t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });
  const [deleteConfirm, setDeleteConfirm] = useState({ show: false, purchaseId: null, purchaseName: '' });

  useEffect(() => {
    loadPurchases();
  }, []);

  // Check for success messages from URL parameters
  useEffect(() => {
    const successParam = searchParams.get('success');
    if (successParam) {
      let message = '';
      switch (successParam) {
        case 'purchase_added':
          message = 'Drug purchase added successfully!';
          break;
        case 'purchase_updated':
          message = 'Drug purchase updated successfully!';
          break;
        default:
          message = 'Operation completed successfully!';
      }

      if (message) {
        setFeedback({ type: 'success', message });

        // Remove the success parameter from URL
        searchParams.delete('success');
        setSearchParams(searchParams, { replace: true });

        // Auto-hide success message after 5 seconds
        setTimeout(() => {
          setFeedback({ type: '', message: '' });
        }, 5000);
      }
    }
  }, [searchParams, setSearchParams]);

  const loadPurchases = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5432/api/v1/drug-purchases');
      if (!response.ok) {
        throw new Error('Failed to fetch purchases');
      }

      const data = await response.json();
      if (data.success && Array.isArray(data.data)) {
        setPurchases(data.data);
      } else {
        setPurchases([]);
      }
    } catch (error) {
      console.error('Error loading purchases:', error);
      setError('Failed to load purchases');
      setPurchases([]);
    } finally {
      setLoading(false);
    }
  };

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const filteredPurchases = useMemo(() => {
    if (!Array.isArray(purchases)) return [];
    return purchases.filter((purchase) => {
      if (!purchase) return false;
      const drugName = purchase.drug_name || '';
      const supplier = purchase.supplier || '';
      const matchesSearch = drugName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           supplier.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [purchases, searchTerm]);

  const chartData = useMemo(() => {
    if (!Array.isArray(filteredPurchases)) return [];
    return filteredPurchases.map((purchase) => ({
      name: purchase.drug_name || 'Unknown',
      quantity: purchase.quantity || 0,
      amount: purchase.total_amount || 0,
    }));
  }, [filteredPurchases]);

  const totalPurchases = Array.isArray(purchases) ? purchases.length : 0;
  const totalQuantity = Array.isArray(purchases) ? purchases.reduce((sum, p) => sum + (Number(p.quantity) || 0), 0) : 0;
  const totalAmount = Array.isArray(purchases) ? purchases.reduce((sum, p) => sum + (Number(p.total_amount) || 0), 0) : 0;

  const handleDelete = (id) => {
    const purchase = purchases.find(p => p.id === id);
    setDeleteConfirm({
      show: true,
      purchaseId: id,
      purchaseName: purchase ? purchase.drug_name : 'Unknown'
    });
  };

  const confirmDelete = async () => {
    const { purchaseId } = deleteConfirm;
    try {
      setFeedback({ type: '', message: '' }); // Clear previous feedback

      const response = await fetch(`http://localhost:5432/api/v1/drug-purchases/${purchaseId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setPurchases(prev => prev.filter(p => p.id !== purchaseId));
        setFeedback({
          type: 'success',
          message: 'Purchase deleted successfully!'
        });

        // Auto-hide success message after 3 seconds
        setTimeout(() => {
          setFeedback({ type: '', message: '' });
        }, 3000);
      } else {
        setFeedback({
          type: 'error',
          message: 'Failed to delete purchase. Please try again.'
        });
      }
    } catch (error) {
      console.error('Error deleting purchase:', error);
      setFeedback({
        type: 'error',
        message: 'Failed to delete purchase. Please check your connection.'
      });
    } finally {
      setDeleteConfirm({ show: false, purchaseId: null, purchaseName: '' });
    }
  };

  const cancelDelete = () => {
    setDeleteConfirm({ show: false, purchaseId: null, purchaseName: '' });
  };

  const handleEdit = (id) => {
    navigate(`/admin/drugs/purchases/edit/${id}`);
  };

  const handleAdd = () => {
    navigate('/admin/drugs/purchases/add');
  };

  if (loading) {
    return (
      <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading purchases...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
        <div className="max-w-7xl mx-auto space-y-6">
          <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => navigate('/admin/drugs')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('drugs.purchases.back_to_drugs')}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('drugs.purchases.title')}</h1>
            </div>
          </div>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400">{error}</p>
              <Button onClick={loadPurchases} className="mt-4">
                {t('drugs.purchases.retry')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('drugs.purchases.back_to_drugs')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('drugs.purchases.title')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('drugs.purchases.manage_inventory')}</p>
          </div>
        </div>

        {/* Feedback Messages */}
        {feedback.message && (
          <div className={`p-4 rounded-lg border ${
            feedback.type === 'success'
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-4 h-4 rounded-full mr-3 ${
                  feedback.type === 'success' ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
                <span className="font-medium">{feedback.message}</span>
              </div>
              <button
                onClick={() => setFeedback({ type: '', message: '' })}
                className={`text-sm font-medium ${
                  feedback.type === 'success' ? 'text-green-600 hover:text-green-800' : 'text-red-600 hover:text-red-800'
                }`}
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        {deleteConfirm.show && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                  <Trash className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{t('drugs.purchases.delete_purchase')}</h3>
                  <p className="text-sm text-gray-500">{t('drugs.purchases.delete_confirmation')}</p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-gray-700">
                  {t('drugs.purchases.delete_warning')} <strong>"{deleteConfirm.purchaseName}"</strong>?
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  {t('drugs.purchases.delete_permanent')}
                </p>
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  variant="secondary"
                  onClick={cancelDelete}
                  className="px-4 py-2"
                >
                  {t('drugs.purchases.cancel')}
                </Button>
                <Button
                  variant="primary"
                  onClick={confirmDelete}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white"
                >
                  <Trash className={`w-4 h-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                  {t('drugs.purchases.delete_purchase_button')}
                </Button>
              </div>
            </div>
          </div>
        )}

        <div
          className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button onClick={loadPurchases} variant="secondary" size="sm">
              {t('drugs.purchases.refresh')}
            </Button>
            <Button onClick={handleAdd} variant="primary" className="border border-orange-600">
              <Plus className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('drugs.purchases.add_purchase')}
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className={`flex flex-col sm:flex-row gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div className="relative flex-1">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder={t('drugs.purchases.search_placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_purchases')}</CardTitle>
              <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalPurchases}</div>
              <p className="text-xs text-muted-foreground">{t('drugs.statistics.purchase_orders')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_quantity')}</CardTitle>
              <Package className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalQuantity}</div>
              <p className="text-xs text-muted-foreground">{t('drugs.statistics.units_purchased')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_amount')}</CardTitle>
              <DollarSign className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalAmount || 0).toFixed(2)}</div>
              <p className="text-xs text-muted-foreground">{t('drugs.statistics.total_spent')}</p>
            </CardContent>
          </Card>
        </div>

        {/* Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.purchases.purchase_overview')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="quantity" fill="#8B5CF6" name={t('drugs.purchases.quantity')} />
                  <Bar dataKey="amount" fill="#3B82F6" name={`${t('drugs.purchases.total_amount')} ($)`} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Purchases Table */}
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.purchases.purchase_history')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('drugs.purchases.drug_name')}</TableHead>
                  <TableHead>{t('drugs.purchases.quantity')}</TableHead>
                  <TableHead>{t('drugs.purchases.transferred')}</TableHead>
                  <TableHead>{t('drugs.purchases.remaining')}</TableHead>
                  <TableHead>{t('drugs.purchases.price_per_unit')}</TableHead>
                  <TableHead>{t('drugs.purchases.total_amount')}</TableHead>
                  <TableHead>{t('drugs.purchases.supplier')}</TableHead>
                  <TableHead>{t('drugs.purchases.purchase_date')}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('drugs.purchases.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPurchases.length > 0 ? (
                  filteredPurchases.map((purchase) => (
                    <TableRow key={purchase.id || Math.random()}>
                      <TableCell className="font-medium">{purchase.drug_name || 'N/A'}</TableCell>
                      <TableCell>{purchase.quantity || 0}</TableCell>
                      <TableCell>
                        <span className="text-blue-600 font-medium">
                          {purchase.transferred_quantity || 0}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className={`font-medium text-lg ${
                            (purchase.remaining_quantity || 0) > 0
                              ? 'text-green-600'
                              : 'text-red-600'
                          }`}>
                            {purchase.remaining_quantity || 0}/{purchase.quantity || 0}
                          </span>
                          {(purchase.remaining_quantity || 0) === 0 && (
                            <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                              {t('drugs.purchases.completed')}
                            </span>
                          )}
                          {(purchase.remaining_quantity || 0) > 0 && (
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                              {t('drugs.purchases.available')}
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {((purchase.remaining_quantity || 0) / (purchase.quantity || 1) * 100).toFixed(0)}{t('drugs.purchases.remaining_percentage')}
                        </div>
                      </TableCell>
                      <TableCell>${(purchase.price_per_unit || 0)}</TableCell>
                      <TableCell>${(purchase.total_amount || 0)}</TableCell>
                      <TableCell>{purchase.supplier || 'N/A'}</TableCell>
                      <TableCell>
                        {purchase.purchase_date
                          ? format(new Date(purchase.purchase_date), 'yyyy-MM-dd')
                          : 'N/A'
                        }
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              className={`flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleEdit(purchase.id)}>
                              <Pencil className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('drugs.purchases.edit')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(purchase.id)}>
                              <Trash className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('drugs.purchases.delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      {t('drugs.purchases.no_purchases_found')}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DrugPurchasesPage;
