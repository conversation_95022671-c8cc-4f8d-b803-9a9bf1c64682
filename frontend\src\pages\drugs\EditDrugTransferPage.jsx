import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useFarm } from '../../contexts/FarmContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';

const EditDrugTransferPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { language, t } = useLanguage();
  const { farms } = useFarm(); // Use farms from FarmContext

  const [formData, setFormData] = useState({
    drug_id: null,
    drug_name: '',
    quantity: '',
    farm_id: null,
    farm_name: '',
    farm_location: '',
    transfer_date: '',
    notes: '',
    status: '',
    price_per_unit: '',
    total_price: '',
    purchase_id: null,
    profit_per_unit: '',
    total_profit: '',
  });
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [fetchError, setFetchError] = useState(null);
  const [transfers, setTransfers] = useState([]);
  const [selectedFarm, setSelectedFarm] = useState(null);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    const fetchTransfer = async () => {
      setLoading(true);
      setFetchError(null);
      try {
        const res = await fetch(`http://localhost:5432/api/v1/drug-transfers/${id}`);
        if (!res.ok) throw new Error('Failed to fetch transfer');
        const json = await res.json();
        if (!json.success || !json.data) throw new Error('Transfer not found');
        const transferData = {
          drug_id: json.data.drug_id || null,
          drug_name: json.data.drug_name || '',
          quantity: json.data.quantity?.toString() || '',
          farm_id: json.data.farm_id || null,
          farm_name: json.data.farm_name || '',
          farm_location: json.data.farm_location || '',
          transfer_date: json.data.transfer_date ? json.data.transfer_date.slice(0, 10) : '',
          notes: json.data.notes || '',
          status: json.data.status || '',
          price_per_unit: json.data.price_per_unit?.toString() || '',
          total_price: json.data.total_price?.toString() || '',
          purchase_id: json.data.purchase_id || null,
          profit_per_unit: json.data.profit_per_unit?.toString() || '',
          total_profit: json.data.total_profit?.toString() || '',
        };

        setFormData(transferData);

        // Set selected farm if farm_id exists
        if (transferData.farm_id && farms.length > 0) {
          const farm = farms.find(f => f.F_Id === transferData.farm_id);
          setSelectedFarm(farm);
        }
      } catch (err) {
        setFetchError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchTransfer();
  }, [id]);

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/drug-transfers')
      .then(res => res.json())
      .then(json => setTransfers(json.data || []));
  }, []);



  // Set selected farm when farms data loads and we have a farm_id
  useEffect(() => {
    if (formData.farm_id && farms.length > 0) {
      const farm = farms.find(f => f.id === formData.farm_id);
      setSelectedFarm(farm);
    }
  }, [farms, formData.farm_id]);

  const allDrugsFromTransfers = transfers;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.drug_name || !formData.drug_name.trim()) newErrors.drug_name = 'Drug name is required';
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = 'Quantity must be at least 1';
    if (!formData.farm_id) newErrors.farm_name = 'Farm selection is required';
    if (!formData.farm_name || !formData.farm_name.trim()) newErrors.farm_name = 'Farm name is missing - please reselect farm';
    if (!formData.transfer_date) newErrors.transfer_date = 'Transfer date is required';
    if (!formData.price_per_unit || parseFloat(formData.price_per_unit) <= 0) newErrors.price_per_unit = 'Price per unit is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle farm selection
    if (name === 'farmId') {
      const farm = farms.find(f => f.id === parseInt(value));
      setSelectedFarm(farm);
      setFormData(prev => ({
        ...prev,
        farm_id: parseInt(value),
        farm_name: farm ? farm.name : '',
        farm_location: farm ? (typeof farm.location === 'object' ?
          `${farm.location.x}, ${farm.location.y}` :
          farm.location) : ''
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    if (!validateForm()) return;
    try {
      setLoading(true);
      // Ensure farm_name is populated if we have farm_id but missing farm_name
      let farmName = formData.farm_name;
      if (!farmName && formData.farm_id && farms.length > 0) {
        const farm = farms.find(f => f.id === formData.farm_id);
        farmName = farm ? farm.name : '';
      }

      const transferData = {
        drug_id: formData.drug_id,
        drug_name: formData.drug_name,
        quantity: parseInt(formData.quantity) || 0,
        farm_id: formData.farm_id,
        farm_name: farmName,
        farm_location: formData.farm_location,
        transfer_date: formData.transfer_date,
        status: formData.status || 'Pending',
        transferred_by: null, // Not used in new system
        notes: formData.notes || null,
      };

      console.log('Form data before submit:', formData);
      console.log('Transfer data being sent:', transferData);

      const response = await fetch(`http://localhost:5432/api/v1/drug-transfers/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transferData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Update error:', errorData);
        throw new Error(errorData.error || 'Failed to update transfer');
      }

      const result = await response.json();
      console.log('Update success:', result);

      // Navigate back with success message
      navigate('/admin/drugs/transfers?success=transfer_updated');
    } catch (error) {
      setSubmitError(error.message || 'Failed to update transfer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-8 text-center text-lg">Loading...</div>;
  }
  if (fetchError) {
    return <div className="p-8 text-center text-red-600">{fetchError}</div>;
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs/transfers')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('drugs.transfers.back_to_transfers')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('drugs.transfers.edit.title')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('drugs.transfers.edit.description')}</p>
          </div>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.transfers.edit.card_title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Drug Name *</label>
                  <select
                    name="drug_name"
                    value={formData.drug_name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.drug_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  >
                    <option value="">Select a drug</option>
                    {allDrugsFromTransfers.map((transfer, idx) => (
                      <option key={transfer.id || idx} value={transfer.drug_name}>
                        {transfer.drug_name}
                      </option>
                    ))}
                  </select>
                  {errors.drug_name && <p className="text-red-500 text-sm mt-1">{errors.drug_name}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantity *</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    min="1"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.quantity ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter quantity"
                  />
                  {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Farm Name *</label>
                  <select
                    name="farmId"
                    value={formData.farm_id || ''}
                    onChange={handleChange}
                    required
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.farm_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                  >
                    <option value="">Select a farm...</option>
                    {farms.map((farm) => (
                      <option key={farm.id} value={farm.id}>
                        {farm.name} - {farm.owner}
                      </option>
                    ))}
                  </select>
                  {farms.length === 0 && (
                    <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-700">
                        <strong>No farms available.</strong> You need to create farms first.<br />
                        <a href="/admin/farms/add" className="text-yellow-800 underline hover:text-yellow-900">Click here to create a farm</a>
                      </p>
                    </div>
                  )}
                  {selectedFarm && (
                    <div className="mt-2 p-3 bg-green-50 rounded-md">
                      <div className="text-sm text-green-700">
                        <div><strong>Farm:</strong> {selectedFarm.name}</div>
                        <div><strong>Owner:</strong> {selectedFarm.owner}</div>
                        <div><strong>Email:</strong> {selectedFarm.email}</div>
                        <div><strong>Phone:</strong> {selectedFarm.phone}</div>
                      </div>
                    </div>
                  )}
                  {errors.farm_name && <p className="text-red-500 text-sm mt-1">{errors.farm_name}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Farm Location</label>
                  <input
                    type="text"
                    name="farm_location"
                    value={formData.farm_location}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                    placeholder="Enter farm location"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transfer Date *</label>
                  <input
                    type="date"
                    name="transfer_date"
                    value={formData.transfer_date}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.transfer_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                  />
                  {errors.transfer_date && <p className="text-red-500 text-sm mt-1">{errors.transfer_date}</p>}
                </div>

                {/* Price Per Unit */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price Per Unit (AFN) *</label>
                  <input
                    type="number"
                    name="price_per_unit"
                    value={formData.price_per_unit}
                    onChange={handleChange}
                    required
                    min="0"
                    step="0.01"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.price_per_unit ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter price per unit"
                  />
                  {errors.price_per_unit && <p className="text-red-500 text-sm mt-1">{errors.price_per_unit}</p>}
                </div>

                {/* Total Price */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Total Price (AFN)</label>
                  <input
                    type="number"
                    name="total_price"
                    value={formData.total_price}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                    placeholder="Total price"
                  />
                </div>

                {/* Purchase ID */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Purchase ID</label>
                  <input
                    type="number"
                    name="purchase_id"
                    value={formData.purchase_id || ''}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                    placeholder="Purchase ID"
                  />
                  <small className="text-gray-500">
                    The ID of the drug purchase this transfer is based on
                  </small>
                </div>

              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  placeholder="Enter any additional notes"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>
              {submitError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{submitError}</p>
                </div>
              )}
              <div className={`flex gap-4 pt-6 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button type="submit" className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {t('drugs.transfers.edit.save_changes')}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/drugs/transfers')}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  {t('drugs.transfers.cancel')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditDrugTransferPage; 