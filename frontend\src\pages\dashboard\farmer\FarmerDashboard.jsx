// api = https://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={API key}



import { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { Link } from 'react-router-dom';
import {
  Calendar,
  Sun,
  Moon,
  Bell,
  Settings,
  Menu,
  CloudRain,
  CloudSun,
  Snowflake,
  Cloud,
  CloudDrizzle,
  X,
  User,
} from 'lucide-react';

// OpenWeatherMap API key
const API_KEY = '********************************';

// Utility to conditionally join class names
const classNames = (...classes) => classes.filter(Boolean).join(' ');

// Get weather icon component
const getWeatherIcon = (main) => {
  switch (main) {
    case 'Clear':
      return <Sun className="h-10 w-10 text-yellow-400" />;
    case 'Clouds':
      return <Cloud className="h-10 w-10 text-gray-400" />;
    case 'Rain':
      return <CloudRain className="h-10 w-10 text-blue-500" />;
    case 'Drizzle':
      return <CloudDrizzle className="h-10 w-10 text-blue-300" />;
    case 'Snow':
      return <Snowflake className="h-10 w-10 text-blue-300" />;
    default:
      return <Cloud className="h-10 w-10 text-gray-400" />;
  }
};

// User avatar component
const UserAvatar = ({ user, size = 8, className = '' }) => (
  <div
    className={classNames(
      `rounded-full bg-primary/10 flex items-center justify-center overflow-hidden`,
      size === 10 ? 'h-10 w-10' : `h-${size} w-${size}`,
      className
    )}
  >
    {user?.image ? (
      <img
        src={`http://localhost:5432/public/images/users/${user.image}`}
        alt={`${user.firstName || 'Farmer'} ${user.lastName || 'User'}`}
        className="h-full w-full object-cover"
        loading="lazy"
      />
    ) : (
      <User className={`text-primary ${size === 10 ? 'h-6 w-6' : 'h-5 w-5'}`} />
    )}
  </div>
);

const FarmerDashboard = () => {
  const { user, logout } = useAuth();
  const { language, t } = useLanguage();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(() =>
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  const [messages] = useState([
    {
      id: 1,
      sender: t('farmer.support'),
      content: t('farmer.hello_help'),
      time: `2 ${t('farmer.minutes_ago')}`,
    },
    {
      id: 2,
      sender: `${user?.firstName} ${user?.lastName}` || t('farmer.you'),
      content: t('farmer.question_irrigation'),
      time: t('farmer.just_now'),
    },
  ]);

  const [weather, setWeather] = useState(null);
  const [forecast, setForecast] = useState([]);
  const [loading, setLoading] = useState(true);

  const toggleDarkMode = () => {
    setIsDarkMode((prev) => {
      const newMode = !prev;
      document.documentElement.classList.toggle('dark', newMode);
      return newMode;
    });
  };

  useEffect(() => {
    document.documentElement.classList.toggle('dark', isDarkMode);
  }, [isDarkMode]);

  const fetchWeather = async (lat, lon) => {
    try {
      const currentRes = await fetch(
        `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric`
      );
      const currentData = await currentRes.json();

      const forecastRes = await fetch(
        `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric`
      );
      const forecastData = await forecastRes.json();

      setWeather({
        temp: currentData.main.temp,
        main: currentData.weather[0].main,
        description: currentData.weather[0].description,
        location: currentData.name,
        icon: getWeatherIcon(currentData.weather[0].main),
      });

      const daily = forecastData.list
        .filter((item) => item.dt_txt.includes('12:00:00'))
        .slice(0, 4)
        .map((item) => ({
          day: new Date(item.dt_txt).toLocaleDateString('en-US', {
            weekday: 'short',
          }),
          temp: Math.round(item.main.temp),
          main: item.weather[0].main,
          icon: getWeatherIcon(item.weather[0].main),
        }));

      setForecast(daily);
    } catch (err) {
      console.error('Error fetching weather:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (pos) => fetchWeather(pos.coords.latitude, pos.coords.longitude),
        () => fetchWeather(31.6289, 65.7372)
      );
    } else {
      fetchWeather(31.6289, 65.7372);
    }
  }, []);

  return (
    <div className={`space-y-6 ${language === 'ps' ? 'rtl' : 'ltr'}`}>


      <section className={`px-4 md:px-6 ${language === 'ps' ? 'text-right' : ''}`}>
        <h1 className="text-2xl font-bold dark:text-white">
          {t('farmer.welcome')}, {user?.firstName || t('farmer.welcome')} {user?.lastName}
        </h1>
        <p className="text-muted-foreground dark:text-gray-300">
          {t('farmer.check_weather_and_chat')}
        </p>
      </section>

      <section className={`flex flex-col md:flex-row gap-6 px-4 md:px-6 ${language === 'ps' ? 'md:flex-row-reverse' : ''}`}>
        <div className="flex-1 rounded-lg border bg-gradient-to-br from-blue-50 to-yellow-50 p-6 dark:from-gray-800 dark:to-gray-900 dark:border-gray-700 shadow">
          <h3 className={`text-lg font-semibold mb-4 text-gray-700 dark:text-gray-200 ${language === 'ps' ? 'text-right' : ''}`}>
            {t('farmer.weather_forecast')}
          </h3>

          {loading ? (
            <p className={`text-sm text-gray-500 dark:text-gray-400 ${language === 'ps' ? 'text-right' : ''}`}>
              {t('farmer.loading_weather')}
            </p>
          ) : weather ? (
            <>
              <div className="flex flex-col items-center text-center bg-white rounded-lg shadow-inner p-6 mb-4 dark:bg-gray-700">
                {weather.icon}
                <p className="text-3xl font-bold text-gray-800 mt-2 dark:text-white">
                  {Math.round(weather.temp)}°C
                </p>
                <p className="text-sm text-muted-foreground dark:text-gray-300">
                  {weather.main}, {weather.location}
                </p>
                <p className="text-xs text-green-600 mt-1 italic dark:text-green-400">
                  {weather.description}
                </p>
              </div>

              <div className="grid grid-cols-4 gap-2 text-center text-sm">
                {forecast.map((day, idx) => (
                  <div
                    key={idx}
                    className="bg-white p-2 rounded shadow-sm dark:bg-gray-700"
                  >
                    <p className="text-xs md:text-sm font-semibold  dark:text-muted">{day.day}</p>
                    <span className='flex justify-center w-full'>
                      {day.icon}
                    </span>
                    <p className="text-xs md:text-sm dark:text-gray-300">{day.temp}°C</p>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <p className={`text-sm text-gray-500 ${language === 'ps' ? 'text-right' : ''}`}>
              {t('farmer.weather_not_available')}
            </p>
          )}
        </div>

       {/* Chat Section */}

        <div className="flex-1 rounded-lg border bg-muted shadow p-6 dark:bg-gray-800 dark:border-gray-700">
          <div className={`flex flex-col gap-3 justify-between items-center mb-4 ${language === 'ps' ? 'text-right' : ''}`}>
            <h3 className="text-base md:text-lg font-bold dark:text-white">{t('farmer.chat_with_support')}</h3>
            <Link
              to="/farmer/chat"
              className={`text-xs md:text-sm text-primary border rounded-md py-2 px-3 dark:border-gray-400 hover:underline flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              {t('farmer.full_chat')} <Calendar className={`h-4 w-4 ${language === 'ps' ? 'mr-1' : 'ml-1'}`} />
            </Link>
          </div>
          <div className="h-64 overflow-y-auto space-y-4 bg-muted/30 p-4 rounded dark:bg-gray-700/50">
            {messages.map((msg) => (
              <div key={msg.id} className="text-sm dark:text-gray-300">
                <p className="font-semibold">{msg.sender}</p>
                <p>{msg.content}</p>
                <p className="text-xs text-muted-foreground">{msg.time}</p>
              </div>
            ))}
          </div>
        </div>
        </section>
        
    </div>
  );
};

export default FarmerDashboard;
