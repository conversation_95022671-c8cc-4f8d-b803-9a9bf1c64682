/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */
import { createContext, useState, useContext, useEffect } from 'react';
import en from '../locales/management/en.json';
import ps from '../locales/management/ps.json';

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(localStorage.getItem('localizeOfManagement') || 'en');

  // Use static imports directly
  const translations = { en, ps };

  // Debug translations on mount
  useEffect(() => {
    console.log('Translations loaded:', translations);
    console.log('EN farmer section:', translations.en?.farmer);
    console.log('PS farmer section:', translations.ps?.farmer);
    console.log('EN farmer welcome:', translations.en?.farmer?.welcome);
  }, []);

  useEffect(() => {
    localStorage.setItem('localizeOfManagement', language);
    document.documentElement.dir = language === 'ps' ? 'rtl' : 'ltr';
  }, [language]);

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === 'en' ? 'ps' : 'en'));
  };

  // Add translation function
  const t = (key, params) => {
    // Test specific farmer keys
    if (key === 'farmer.welcome') {
      console.log('🔍 Testing farmer.welcome specifically');
      console.log('Language:', language);
      console.log('Translations object:', translations);
      console.log('EN translations:', translations.en);
      console.log('EN farmer section:', translations.en?.farmer);
      console.log('EN farmer welcome:', translations.en?.farmer?.welcome);
      console.log('PS farmer welcome:', translations.ps?.farmer?.welcome);
    }

    console.log('Translation request:', key, 'Language:', language);
    console.log('Translations available:', !!translations);
    console.log('Language data available:', !!translations?.[language]);
    console.log('Translation keys count:', Object.keys(translations?.[language] || {}).length);

    if (!translations || !translations[language] || Object.keys(translations[language]).length === 0) {
      console.log('No translations for language:', language, 'Available:', Object.keys(translations || {}));
      return key;
    }

    // Support nested keys like 'sidebar.main'
    const keys = key.split('.')
    let value = translations[language]

    console.log('Looking for keys:', keys, 'in', Object.keys(value || {}));

    for (const k of keys) {
      console.log(`Looking for key "${k}" in:`, value);
      value = value?.[k]
      if (value === undefined) {
        console.log('Key not found:', key, 'in language:', language, 'Available keys at this level:', Object.keys(value || {}));
        return key;
      }
    }

    console.log('Found translation:', value);

    // Interpolate params if provided
    if (params && typeof value === 'string') {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        value = value.replace(new RegExp(`\\{${paramKey}\\}`, 'g'), paramValue)
      })
    }
    return value
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, toggleLanguage, translations, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);
