/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */
import { createContext, useState, useContext, useEffect } from 'react';
import en from '../locales/management/en.json';
import ps from '../locales/management/ps.json';

const LanguageContext = createContext();

const translations = { en, ps };

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(localStorage.getItem('localizeOfManagement') || 'en');

  useEffect(() => {
    localStorage.setItem('localizeOfManagement', language);
    document.documentElement.dir = language === 'ps' ? 'rtl' : 'ltr';
  }, [language]);

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === 'en' ? 'ps' : 'en'));
  };

  // Add translation function
  const t = (key, params) => {
    if (!translations || !translations[language]) return key;
    // Support nested keys like 'sidebar.main'
    const keys = key.split('.')
    let value = translations[language]
    for (const k of keys) {
      value = value?.[k]
      if (value === undefined) return key
    }
    // Interpolate params if provided
    if (params && typeof value === 'string') {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        value = value.replace(new RegExp(`\\{${paramKey}\\}`, 'g'), paramValue)
      })
    }
    return value
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, toggleLanguage, translations, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);

// Export translations for use in other files
export { translations };
