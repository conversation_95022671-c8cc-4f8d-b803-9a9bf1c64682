/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */
import { createContext, useState, useContext, useEffect } from 'react';
// Try dynamic imports instead
const loadTranslations = async () => {
  try {
    const enModule = await import('../locales/management/en.json');
    const psModule = await import('../locales/management/ps.json');
    return {
      en: enModule.default || enModule,
      ps: psModule.default || psModule
    };
  } catch (error) {
    console.error('Error loading translations:', error);
    return { en: {}, ps: {} };
  }
};

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(localStorage.getItem('localizeOfManagement') || 'en');
  const [translations, setTranslations] = useState({ en: {}, ps: {} });

  // Load translations on component mount
  useEffect(() => {
    const initTranslations = async () => {
      const loadedTranslations = await loadTranslations();
      console.log('Translations loaded:', loadedTranslations);
      console.log('EN farmer section:', loadedTranslations.en?.farmer);
      console.log('PS farmer section:', loadedTranslations.ps?.farmer);
      setTranslations(loadedTranslations);
    };

    initTranslations();
  }, []);

  useEffect(() => {
    localStorage.setItem('localizeOfManagement', language);
    document.documentElement.dir = language === 'ps' ? 'rtl' : 'ltr';
  }, [language]);

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === 'en' ? 'ps' : 'en'));
  };

  // Add translation function
  const t = (key, params) => {
    console.log('Translation request:', key, 'Language:', language);
    console.log('Translations available:', !!translations);
    console.log('Language data available:', !!translations?.[language]);
    console.log('Translation keys count:', Object.keys(translations?.[language] || {}).length);

    if (!translations || !translations[language] || Object.keys(translations[language]).length === 0) {
      console.log('No translations for language:', language, 'Available:', Object.keys(translations || {}));
      return key;
    }

    // Support nested keys like 'sidebar.main'
    const keys = key.split('.')
    let value = translations[language]

    console.log('Looking for keys:', keys, 'in', Object.keys(value || {}));

    for (const k of keys) {
      value = value?.[k]
      if (value === undefined) {
        console.log('Key not found:', key, 'in language:', language, 'Available keys at this level:', Object.keys(value || {}));
        return key;
      }
    }

    console.log('Found translation:', value);

    // Interpolate params if provided
    if (params && typeof value === 'string') {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        value = value.replace(new RegExp(`\\{${paramKey}\\}`, 'g'), paramValue)
      })
    }
    return value
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, toggleLanguage, translations, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);
