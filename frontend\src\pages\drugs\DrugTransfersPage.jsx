'use client';

import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Plus,
  Search,
  MoreVertical,
  Activity,
  Package,
  MapPin,
  Trash,
  Pencil,
  ArrowLeft,
} from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend } from 'recharts';
import { format } from 'date-fns';

const DrugTransfersPage = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { language, t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [transfers, setTransfers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });
  const [deleteConfirm, setDeleteConfirm] = useState({ show: false, transferId: null, transferName: '' });

  useEffect(() => {
    loadTransfers();
  }, []);

  // Check for success messages from URL parameters
  useEffect(() => {
    const successParam = searchParams.get('success');
    if (successParam) {
      let message = '';
      switch (successParam) {
        case 'transfer_added':
          message = t('drugs.transfers.messages.transfer_created_successfully');
          break;
        case 'transfer_updated':
          message = t('drugs.transfers.messages.transfer_updated_successfully');
          break;
        default:
          message = t('drugs.transfers.messages.operation_completed');
      }

      if (message) {
        setFeedback({ type: 'success', message });

        // Remove the success parameter from URL
        searchParams.delete('success');
        setSearchParams(searchParams, { replace: true });

        // Auto-hide success message after 5 seconds
        setTimeout(() => {
          setFeedback({ type: '', message: '' });
        }, 5000);
      }
    }
  }, [searchParams, setSearchParams]);

  const loadTransfers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5432/api/v1/drug-transfers');
      if (!response.ok) {
        throw new Error('Failed to fetch transfers');
      }

      const data = await response.json();
      if (data.success && Array.isArray(data.data)) {
        setTransfers(data.data);
      } else {
        setTransfers([]);
      }
    } catch (error) {
      console.error('Error loading transfers:', error);
      setError('Failed to load transfers');
      setTransfers([]);
    } finally {
      setLoading(false);
    }
  };

  const filteredTransfers = useMemo(() => {
    if (!Array.isArray(transfers)) return [];
    return transfers.filter((transfer) => {
      if (!transfer) return false;
      const drugName = transfer.drug_name || '';
      const farmName = transfer.farm_name || '';
      const farmLocation = transfer.farm_location || '';
      const matchesSearch = drugName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           farmName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           farmLocation.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [transfers, searchTerm]);

  const chartData = useMemo(() => {
    if (!Array.isArray(filteredTransfers)) return [];
    return filteredTransfers.map((transfer) => ({
      name: transfer.farm_name || 'Unknown Farm',
      quantity: transfer.quantity || 0,
    }));
  }, [filteredTransfers]);

  const totalTransfers = Array.isArray(transfers) ? transfers.length : 0;
  const totalQuantity = Array.isArray(transfers) ? transfers.reduce((sum, t) => sum + (Number(t.quantity) || 0), 0) : 0;
  const completedTransfers = Array.isArray(transfers) ? transfers.filter(t => t.status === 'Completed').length : 0;
  const totalProfit = Array.isArray(transfers) ? transfers.reduce((sum, t) => sum + (Number(t.total_profit) || 0), 0) : 0;

  const handleDelete = (id) => {
    const transfer = transfers.find(t => t.id === id);
    setDeleteConfirm({
      show: true,
      transferId: id,
      transferName: transfer ? `${transfer.drug_name} to ${transfer.farm_name}` : 'Unknown Transfer'
    });
  };

  const confirmDelete = async () => {
    const { transferId } = deleteConfirm;
    try {
      setFeedback({ type: '', message: '' }); // Clear previous feedback

      const response = await fetch(`http://localhost:5432/api/v1/drug-transfers/${transferId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setTransfers(prev => prev.filter(t => t.id !== transferId));
        setFeedback({
          type: 'success',
          message: 'Transfer deleted successfully!'
        });

        // Auto-hide success message after 3 seconds
        setTimeout(() => {
          setFeedback({ type: '', message: '' });
        }, 3000);
      } else {
        setFeedback({
          type: 'error',
          message: 'Failed to delete transfer. Please try again.'
        });
      }
    } catch (error) {
      console.error('Error deleting transfer:', error);
      setFeedback({
        type: 'error',
        message: 'Failed to delete transfer. Please check your connection.'
      });
    } finally {
      setDeleteConfirm({ show: false, transferId: null, transferName: '' });
    }
  };

  const cancelDelete = () => {
    setDeleteConfirm({ show: false, transferId: null, transferName: '' });
  };

  const handleEdit = (id) => {
    navigate(`/admin/drugs/transfers/edit/${id}`);
  };

  const handleAdd = () => {
    navigate('/admin/drugs/transfers/add');
  };

  if (loading) {
    return (
      <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading transfers...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
        <div className="max-w-7xl mx-auto space-y-6">
          <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => navigate('/admin/drugs')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Drugs
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Drug Transfers</h1>
            </div>
          </div>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400">{error}</p>
              <Button onClick={loadTransfers} className="mt-4">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('drugs.transfers.back_to_drugs')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('drugs.transfers.title')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('drugs.transfers.description')}</p>
          </div>
        </div>

        {/* Feedback Messages */}
        {feedback.message && (
          <div className={`p-4 rounded-lg border ${
            feedback.type === 'success'
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-4 h-4 rounded-full mr-3 ${
                  feedback.type === 'success' ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
                <span className="font-medium">{feedback.message}</span>
              </div>
              <button
                onClick={() => setFeedback({ type: '', message: '' })}
                className={`text-sm font-medium ${
                  feedback.type === 'success' ? 'text-green-600 hover:text-green-800' : 'text-red-600 hover:text-red-800'
                }`}
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        {deleteConfirm.show && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                  <Trash className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{t('drugs.transfers.delete_transfer')}</h3>
                  <p className="text-sm text-gray-500">{t('drugs.transfers.delete_confirmation')}</p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-gray-700">
                  {t('drugs.transfers.delete_warning')} <strong>"{deleteConfirm.transferName}"</strong>?
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  {t('drugs.transfers.delete_permanent')}
                </p>
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  variant="secondary"
                  onClick={cancelDelete}
                  className="px-4 py-2"
                >
                  {t('drugs.transfers.cancel')}
                </Button>
                <Button
                  variant="primary"
                  onClick={confirmDelete}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white"
                >
                  <Trash className={`w-4 h-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                  {t('drugs.transfers.delete_transfer_button')}
                </Button>
              </div>
            </div>
          </div>
        )}

        <div
          className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button onClick={loadTransfers} variant="secondary" size="sm">
              {t('drugs.transfers.refresh')}
            </Button>
            <Button onClick={handleAdd} variant="primary" className="border border-orange-600">
              <Plus className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('drugs.transfers.add_transfer')}
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className={`flex flex-col sm:flex-row gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div className="relative flex-1">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder={t('drugs.transfers.search_placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_transfers')}</CardTitle>
              <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalTransfers}</div>
              <p className="text-xs text-muted-foreground">{t('drugs.statistics.transfer_orders')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_quantity')}</CardTitle>
              <Package className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalQuantity}</div>
              <p className="text-xs text-muted-foreground">{t('drugs.statistics.units_transferred')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('drugs.transfers.total_profit_loss')}</CardTitle>
              <MapPin className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${
                totalProfit > 0 ? 'text-green-600' :
                totalProfit < 0 ? 'text-red-600' :
                'text-gray-600'
              }`}>
                {totalProfit > 0 ? '+' : ''}{totalProfit.toFixed(2)} AFN
              </div>
              <p className="text-xs text-muted-foreground">
                {totalProfit > 0 ? t('drugs.transfers.total_profit') : totalProfit < 0 ? t('drugs.transfers.total_loss') : t('drugs.transfers.break_even')}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.transfers.transfer_distribution_by_farm')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="quantity" fill="#8B5CF6" name={t('drugs.transfers.quantity_transferred')} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Transfers Table */}
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.transfers.transfer_history')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('drugs.transfers.drug_name')}</TableHead>
                  <TableHead>{t('drugs.transfers.quantity')}</TableHead>
                  <TableHead>{t('drugs.transfers.farm_name')}</TableHead>
                  <TableHead>{t('drugs.transfers.location')}</TableHead>
                  <TableHead>{t('drugs.transfers.transfer_date')}</TableHead>
                  <TableHead>{t('drugs.transfers.price_afn')}</TableHead>
                  <TableHead>{t('drugs.transfers.profit_loss')}</TableHead>
                  <TableHead>{t('drugs.transfers.status')}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('drugs.transfers.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTransfers.length > 0 ? (
                  filteredTransfers.map((transfer) => (
                    <TableRow key={transfer.id || Math.random()}>
                      <TableCell className="font-medium">{transfer.drug_name || 'N/A'}</TableCell>
                      <TableCell>{transfer.quantity || 0}</TableCell>
                      <TableCell>{transfer.farm_name || 'Unknown Farm'}</TableCell>
                      <TableCell>
                        {transfer.farm_location ?
                          (transfer.farm_location.includes(',') ?
                            `Coordinates: ${transfer.farm_location}` :
                            transfer.farm_location) :
                          'N/A'
                        }
                      </TableCell>
                      <TableCell>
                        {transfer.transfer_date
                          ? format(new Date(transfer.transfer_date), 'yyyy-MM-dd')
                          : 'N/A'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>Total: {transfer.total_price ? `${transfer.total_price} AFN` : 'N/A'}</div>
                          <div className="text-gray-500">
                            Per Unit: {transfer.price_per_unit ? `${transfer.price_per_unit} AFN` : 'N/A'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {transfer.total_profit !== null && transfer.total_profit !== undefined ? (
                          <div className={`text-sm font-medium ${
                            transfer.total_profit > 0 ? 'text-green-600' :
                            transfer.total_profit < 0 ? 'text-red-600' :
                            'text-gray-600'
                          }`}>
                            <div>
                              {transfer.total_profit > 0 ? '+' : ''}{transfer.total_profit} AFN
                            </div>
                            <div className="text-xs text-gray-500">
                              {transfer.profit_per_unit > 0 ? '+' : ''}{transfer.profit_per_unit || 0} per unit
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400">N/A</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          transfer.status === 'Completed'
                            ? 'bg-green-100 text-green-800'
                            : transfer.status === 'In Transit'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {transfer.status || 'Pending'}
                        </span>
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              className={`flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleEdit(transfer.id)}>
                              <Pencil className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('drugs.transfers.edit_button')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(transfer.id)}>
                              <Trash className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('drugs.transfers.delete_button')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      No transfers found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DrugTransfersPage;
