/* eslint-disable no-undef */
'use client';

import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import ErrorBoundary from './ErrorBoundary';
import LoadingSpinner from './LoadingSpinner';
import Button from './Button';
import Modal from './Modal';
import Logo from './Logo';
import { io } from 'socket.io-client';
import { Globe, ChevronDown } from 'lucide-react';

// Set base URL for axios
axios.defaults.baseURL = 'http://localhost:5432/api/v1';

// Add token to all requests
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

function Chat() {
  const navigate = useNavigate();
  const location = useLocation();
  const scrollRef = useRef(null);
  const searchParams = new URLSearchParams(location.search);
  const roomId = searchParams.get('roomId');
  const { user } = useAuth();
  const { language, toggleLanguage, t } = useLanguage();

  const [userRole, setUserRole] = useState(null);
  const [userId, setUserId] = useState(null);
  const [activeTab, setActiveTab] = useState('farmers');
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [farmerRooms, setFarmerRooms] = useState([]);
  const [shopperRooms, setShopperRooms] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentRoom, setCurrentRoom] = useState(null);
  const [error, setError] = useState(null);
  const [showBroadcastModal, setShowBroadcastModal] = useState(false);
  const [broadcastMessage, setBroadcastMessage] = useState('');
  const [broadcastType, setBroadcastType] = useState('farmer');

  // Chat notifications state
  const [chatNotifications, setChatNotifications] = useState([]);
  const [unreadChatCount, setUnreadChatCount] = useState(0);
  const [showNotifications, setShowNotifications] = useState(false);
  const [markingAsRead, setMarkingAsRead] = useState(null); // Track which notification is being marked as read
  const [fcount, setFcount] = useState('0');
  const [scount, setScount] = useState('0');

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [allFarmers, setAllFarmers] = useState([]);
  const [allShoppers, setAllShoppers] = useState([]);

  // New state for admin selection
  const [showAdminSelectModal, setShowAdminSelectModal] = useState(false);
  const [admins, setAdmins] = useState([]);

  // Language dropdown state
  const [isLangOpen, setIsLangOpen] = useState(false);
  const langDropdownRef = useRef(null);
  const [loadingAdmins, setLoadingAdmins] = useState(false);
  const [adminFetchError, setAdminFetchError] = useState(null);

  // Fetch chat notifications for admin
  const fetchChatNotifications = async () => {
    if (userRole !== 'admin') return;

    try {
      const response = await axios.get('http://localhost:5432/api/v1/chat-notifications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        setChatNotifications(response.data.data);
        const unreadCount = response.data.data.filter(n => !n.read_status).length;
        setUnreadChatCount(unreadCount);
      }
    } catch (error) {
      console.error('Error fetching chat notifications:', error);
    }
  };

  // Fetch all farmers for admin
  const fetchAllFarmers = async () => {
    if (userRole !== 'admin') return;

    try {
      const response = await axios.get('http://localhost:5432/api/v1/users/farmers');
      if (response.data.success) {
        setAllFarmers(response.data.data);
        setFcount(response.data.data.length.toString());
      }
    } catch (error) {
      console.error('Error fetching farmers:', error);
    }
  };

  // Fetch all shoppers for admin
  const fetchAllShoppers = async () => {
    if (userRole !== 'admin') return;

    try {
      const response = await axios.get('http://localhost:5432/api/v1/users/shoppers');
      if (response.data.success) {
        setAllShoppers(response.data.data);
        setScount(response.data.data.length.toString());
      }
    } catch (error) {
      console.error('Error fetching shoppers:', error);
    }
  };

  // Filter users based on search query
  const getFilteredUsers = (users) => {
    if (!searchQuery.trim()) return users;

    return users.filter(user => {
      const fullName = `${user.U_FirstName || ''} ${user.U_LastName || ''}`.toLowerCase();
      const email = (user.U_Email || '').toLowerCase();
      const query = searchQuery.toLowerCase();

      return fullName.includes(query) || email.includes(query);
    });
  };

  // Start chat with a user (create room if doesn't exist)
  const startChatWithUser = async (user) => {
    try {
      console.log('🚀 Starting chat with user:', user);

      const response = await axios.post('http://localhost:5432/api/v1/chats/start', {
        user2_id: user.u_Id
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Start chat response:', response.data);

      if (response.data.success) {
        // The API returns roomId directly, not data.room_id
        const roomId = response.data.roomId || response.data.data?.room_id;
        console.log('🏠 Room ID:', roomId);

        if (roomId) {
          navigate(`${location.pathname}?roomId=${roomId}`);
          // Refresh rooms to show the new chat
          fetchRooms();
        } else {
          console.error('❌ No room ID in response');
        }
      } else {
        console.error('❌ API returned success: false');
      }
    } catch (error) {
      console.error('❌ Error starting chat:', error);
      console.error('Error details:', error.response?.data);
    }
  };

  // Mark chat notification as read
  const markChatNotificationAsRead = async (notificationId) => {
    try {
      console.log(`🔔 Marking notification ${notificationId} as read...`);

      await axios.patch(`http://localhost:5432/api/v1/chat-notifications/${notificationId}/read`, {}, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`✅ Notification ${notificationId} marked as read`);

      // Update local state immediately
      setChatNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId ? { ...notif, read_status: true } : notif
        )
      );
      setUnreadChatCount(prev => Math.max(0, prev - 1));

      return true; // Return success
    } catch (error) {
      console.error('Error marking chat notification as read:', error);
      return false; // Return failure
    }
  };

  // Mark all chat notifications as read
  const markAllChatNotificationsAsRead = async () => {
    try {
      await axios.patch('http://localhost:5432/api/v1/chat-notifications/mark-all-read', {}, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      // Update local state
      setChatNotifications(prev =>
        prev.map(notif => ({ ...notif, read_status: true }))
      );
      setUnreadChatCount(0);
    } catch (error) {
      console.error('Error marking all chat notifications as read:', error);
    }
  };

  // Mark all notifications for a specific room as read (when admin visits room)
  const markRoomNotificationsAsRead = async (roomId) => {
    if (userRole !== 'admin' || !roomId) return;

    try {
      const response = await axios.patch(`http://localhost:5432/api/v1/chat-notifications/room/${roomId}/mark-read`, {}, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success && response.data.markedCount > 0) {
        console.log(`✅ Marked ${response.data.markedCount} notifications as read for room ${roomId}`);

        // Update local state - mark notifications for this room as read
        setChatNotifications(prev =>
          prev.map(notif =>
            notif.room_id === parseInt(roomId) ? { ...notif, read_status: true } : notif
          )
        );

        // Update unread count
        const markedCount = response.data.markedCount;
        setUnreadChatCount(prev => Math.max(0, prev - markedCount));
      }
    } catch (error) {
      console.error('Error marking room notifications as read:', error);
    }
  };

  // New states for editing and deleting messages
  const [editingMessageId, setEditingMessageId] = useState(null);
  const [editingContent, setEditingContent] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [messageToDelete, setMessageToDelete] = useState(null);
  const [socket, setSocket] = useState(null);

  // Use user info from AuthContext
  useEffect(() => {
    if (user) {
      setUserRole(user.role.toLowerCase());
      setUserId(user.u_Id || user.id);
    }
  }, [user]);

  // Close language dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (langDropdownRef.current && !langDropdownRef.current.contains(event.target)) {
        setIsLangOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Load chat rooms for admin
  useEffect(() => {
    if (userRole !== 'admin') return;

    const fetchRooms = async () => {
      try {
        setLoading(true);
        const [farmerResponse, shopperResponse] = await Promise.all([
          axios.get('http://localhost:5432/api/v1/chats/admin/farmer-chats'),
          axios.get('http://localhost:5432/api/v1/chats/admin/shopper-chats'),
        ]);

        setFarmerRooms(farmerResponse.data.rooms || []);
        setShopperRooms(shopperResponse.data.rooms || []);
        setFcount(farmerResponse.data.count || []);
        setScount(shopperResponse.data.count || []);
      } catch (error) {
        console.error('Error fetching rooms:', error);
        setError('Failed to load chat rooms');
      } finally {
        setLoading(false);
      }
    };

    fetchRooms();
    fetchChatNotifications(); // Also fetch chat notifications
    fetchAllFarmers(); // Fetch all farmers for admin
    fetchAllShoppers(); // Fetch all shoppers for admin
  }, [userRole]);

  // Auto-refresh chat notifications every 30 seconds for admin
  useEffect(() => {
    if (userRole !== 'admin') return;

    const interval = setInterval(() => {
      fetchChatNotifications();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [userRole]);

  // Load messages when room changes
  useEffect(() => {
    if (!roomId) {
      setMessages([]);
      setCurrentRoom(null);
      return;
    }

    const fetchMessages = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`http://localhost:5432/api/v1/chats/${roomId}/messages`);
        setMessages(response.data.data || []);

        // Find current room details
        const allRooms = [...farmerRooms, ...shopperRooms];
        const room = allRooms.find((r) => r.room_id.toString() === roomId);

        setCurrentRoom(room || { room_id: roomId });

        // 🔔 SMART NOTIFICATION LOGIC: Auto-mark room notifications as read when admin visits room
        if (userRole === 'admin') {
          markRoomNotificationsAsRead(roomId);
        }

        // Scroll to bottom
        setTimeout(() => {
          if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
          }
        }, 100);
      } catch (error) {
        console.error('Error fetching messages:', error);
        setError('Failed to load messages');
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();

    // Set up polling for new messages (reduced frequency since we have Socket.IO)
    const interval = setInterval(fetchMessages, 10000);
    return () => clearInterval(interval);
  }, [roomId, farmerRooms, shopperRooms, userRole]);

  // Socket.IO connection and real-time updates
  useEffect(() => {
    const newSocket = io('http://localhost:5432');
    setSocket(newSocket);

    // Join the current room if we have one
    if (roomId) {
      newSocket.emit('join_room', roomId);
    }

    // Listen for new messages
    newSocket.on('new_message', (newMessage) => {
      setMessages(prev => [...prev, newMessage]);

      // Refresh chat notifications for admin when new message arrives
      if (userRole === 'admin') {
        fetchChatNotifications();
      }

      // Scroll to bottom
      setTimeout(() => {
        if (scrollRef.current) {
          scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
      }, 100);
    });

    // Listen for message deletions
    newSocket.on('message_deleted', ({ messageId }) => {
      setMessages(prev => prev.filter(msg => msg.message_id !== messageId));
    });

    // Listen for message edits
    newSocket.on('message_edited', (editedMessage) => {
      setMessages(prev => prev.map(msg =>
        msg.message_id === editedMessage.message_id ? editedMessage : msg
      ));
    });

    return () => {
      if (roomId) {
        newSocket.emit('leave_room', roomId);
      }
      newSocket.disconnect();
    };
  }, [roomId]);

  // Functions for message editing and deletion
  const startEditMessage = (message) => {
    setEditingMessageId(message.message_id);
    setEditingContent(message.message_content);
  };

  const cancelEdit = () => {
    setEditingMessageId(null);
    setEditingContent('');
  };

  const saveEdit = async () => {
    if (!editingContent.trim()) return;

    try {
      await axios.put(`http://localhost:5432/api/v1/chats/messages/${editingMessageId}`, {
        message_content: editingContent.trim(),
      });

      setEditingMessageId(null);
      setEditingContent('');
    } catch (error) {
      console.error('Error editing message:', error);
      setError('Failed to edit message');
    }
  };

  const confirmDelete = (message) => {
    setMessageToDelete(message);
    setShowDeleteModal(true);
  };

  const deleteMessage = async () => {
    if (!messageToDelete) return;

    try {
      await axios.delete(`http://localhost:5432/api/v1/chats/messages/${messageToDelete.message_id}`);
      setShowDeleteModal(false);
      setMessageToDelete(null);
    } catch (error) {
      console.error('Error deleting message:', error);
      setError('Failed to delete message');
    }
  };

  // Fetch admins list
  const fetchAdmins = async () => {
    try {
      console.log('Fetching admins...');
      setLoadingAdmins(true);
      setAdminFetchError(null);

      try {
        const response = await axios.get('http://localhost:5432/api/v1/users/admins');
        console.log('Admin API response:', response.data);

        if (response.data.success && response.data.data) {
          setAdmins(response.data.data);
        } else {
          console.warn('API returned success: false or no data. Using mock data.');
          setAdmins(mockAdmins);
          setAdminFetchError('Could not load admins from server. Using backup data.');
        }
      } catch (error) {
        console.error('Error fetching admins from API:', error);
        setAdmins(mockAdmins);
        setAdminFetchError('Could not connect to server. Using backup data.');
      }
    } finally {
      setLoadingAdmins(false);
    }
  };

  // Handle image selection
  const handleImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Clear image selection
  const clearImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
  };

  // Send message
  const sendMessage = async () => {
    if ((!message.trim() && !selectedImage) || !roomId) return;

    try {
      setLoading(true);

      const formData = new FormData();
      formData.append('room_id', roomId);
      let type = 'text';
      if (selectedImage && message.trim()) {
        type = 'both';
      } else if (selectedImage) {
        type = 'image';
      }

      formData.append('message_type', type);

      formData.append('message_content', message);

      if (selectedImage) {
        formData.append('image', selectedImage);
      }

      await axios.post('http://localhost:5432/api/v1/chats/send', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Clear inputs
      setMessage('');
      clearImage();

      // Refresh messages
      const response = await axios.get(`http://localhost:5432/api/v1/chats/${roomId}/messages`);
      setMessages(response.data.data || []);

      // Scroll to bottom
      setTimeout(() => {
        if (scrollRef.current) {
          scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
      }, 100);
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  // Open admin selection modal
  const openAdminSelectModal = () => {
    console.log('Opening admin selection modal');
    // First set the modal to visible, then fetch admins
    setShowAdminSelectModal(true);
    fetchAdmins().catch((err) => {
      console.error('Error in fetchAdmins:', err);
      setAdminFetchError('Failed to load admin list. Please try again.');
    });
  };

  // Start a new chat (for farmers/shoppers)
  const startChat = async (adminId) => {
    try {
      console.log('Starting chat with admin ID:', adminId);
      setLoading(true);
      setShowAdminSelectModal(false);

      try {
        // Try to create a new chat room
        const response = await axios.post('http://localhost:5432/api/v1/chats/start', {
          user2_id: adminId,
        });

        console.log('Start chat response:', response.data);

        if (response.data.success && response.data.roomId) {
          // New room created successfully
          navigate(`${location.pathname}?roomId=${response.data.roomId}`);
          return;
        }
      } catch (error) {
        // Check if this is the "already exists" error
        if (
          error.response &&
          error.response.status === 200 &&
          error.response.data &&
          error.response.data.message &&
          error.response.data.message.includes('already exists')
        ) {
          console.log('Chat room already exists, using existing room:', error.response.data);

          // Extract room ID from the error response
          const existingRoomId = error.response.data.roomId;

          if (existingRoomId) {
            // Navigate to the existing room
            navigate(`${location.pathname}?roomId=${existingRoomId}`);
            return;
          } else {
            // Room ID not found in the error response
            throw new Error('Existing room ID not found in response');
          }
        } else {
          // This is some other error, rethrow it
          throw error;
        }
      }

      // If we get here, something went wrong
      throw new Error('Failed to start or find chat room');
    } catch (error) {
      console.error('Error starting chat:', error);
      setError(`Failed to start chat: ${error.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle key press (Enter to send)
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Send broadcast message
  const sendBroadcastMessage = async () => {
    if (!broadcastMessage.trim()) return;

    try {
      setLoading(true);
      const res = await axios.post(`http://localhost:5432/api/v1/chats/admin/send-${broadcastType}`, {
        message_content: broadcastMessage,
      });

      setBroadcastMessage('');
      setShowBroadcastModal(false);
      alert(`${res.data.message}`);
    } catch (error) {
      console.error(`Error sending message to ${broadcastType}s:`, error);
      setError(`Failed to send message to ${broadcastType}s`);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !messages.length && !roomId) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-red-500 text-xl mb-4">⚠️ Error</div>
        <p className="text-red-600 mb-4">{error}</p>
        <Button
          onClick={() => setError(null)}
          className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className={`flex flex-col rounded-md w-[70vw] h-[80vh] bg-muted dark:bg-secondary ${language === 'ps' ? 'rtl' : 'ltr'}`}>
        <div className="flex rounded-md items-center justify-between dark:bg-secondary w-full p-4 bg-muted border-b dark:border-b-gray-600">
          <div className="flex items-center dark:bg-secondary gap-2">
            <Button
              onClick={() => navigate(userRole === 'admin' ? '/admin' : userRole === 'farmer' ? '/farmer' : '/shopper')}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md"
            >
              Back
            </Button>

            {/* Chat Notifications Bell for Admin */}
            {userRole === 'admin' && (
              <div className="relative">
                <Button
                  onClick={() => setShowNotifications(!showNotifications)}
                  className={`px-3 py-2 text-sm rounded-md transition-colors relative ${
                    unreadChatCount > 0
                      ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                  }`}
                >
                  🔔
                  {unreadChatCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-yellow-400 text-yellow-900 text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                      {unreadChatCount > 99 ? '99+' : unreadChatCount}
                    </span>
                  )}
                </Button>

                {/* Notifications Dropdown */}
                {showNotifications && (
                  <div className="absolute left-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                    <div className="p-3 border-b border-gray-200 flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">Chat Notifications</h3>
                      {unreadChatCount > 0 && (
                        <Button
                          onClick={markAllChatNotificationsAsRead}
                          className="text-xs px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded"
                        >
                          Mark All Read
                        </Button>
                      )}
                    </div>

                    <div className="max-h-64 overflow-y-auto">
                      {chatNotifications.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          <div className="text-2xl mb-2">💬</div>
                          <p>No chat notifications</p>
                        </div>
                      ) : (
                        chatNotifications.slice(0, 10).map(notification => (
                          <div
                            key={notification.id}
                            className={`p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
                              !notification.read_status ? 'bg-yellow-50 border-l-4 border-l-yellow-500' : ''
                            }`}
                            onClick={async () => {
                              if (notification.room_id && markingAsRead !== notification.id) {
                                try {
                                  setMarkingAsRead(notification.id); // Show loading state

                                  // First mark as read, then navigate
                                  const success = await markChatNotificationAsRead(notification.id);

                                  if (success) {
                                    navigate(`${location.pathname}?roomId=${notification.room_id}`);
                                    setShowNotifications(false);
                                  }
                                } finally {
                                  setMarkingAsRead(null); // Clear loading state
                                }
                              }
                            }}
                          >
                            <div className="flex items-start space-x-2">
                              <div className="flex-shrink-0">
                                <span className={`inline-block w-2 h-2 rounded-full ${
                                  notification.sender_role === 'farmer' ? 'bg-green-500' : 'bg-purple-500'
                                }`}></span>
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {notification.title}
                                </p>
                                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                  {notification.message}
                                </p>
                                <div className="flex items-center justify-between mt-2">
                                  <span className={`text-xs px-2 py-1 rounded ${
                                    notification.sender_role === 'farmer'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-purple-100 text-purple-800'
                                  }`}>
                                    {notification.sender_role}
                                  </span>
                                  <span className="text-xs text-gray-400">
                                    {new Date(notification.created_at).toLocaleTimeString()}
                                  </span>
                                </div>
                              </div>
                              {!notification.read_status && (
                                <div className="flex-shrink-0">
                                  {markingAsRead === notification.id ? (
                                    <div className="w-2 h-2 border border-yellow-500 border-t-transparent rounded-full animate-spin"></div>
                                  ) : (
                                    <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    {chatNotifications.length > 10 && (
                      <div className="p-3 border-t border-gray-200 text-center">
                        <Button
                          onClick={() => {
                            navigate('/admin/notifications');
                            setShowNotifications(false);
                          }}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          View All Notifications
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* <Logo size="small" /> */}
            {/* <h1 className="text-xl font-semibold">Chat</h1> */}
          </div>

          {userRole === 'admin' && (
            <div className="flex gap-2">
              <button
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'farmers' ? 'bg-green-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => setActiveTab('farmers')}
              >
                {t('chat.farmers')}
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'shoppers' ? 'bg-green-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => setActiveTab('shoppers')}
              >
                {t('chat.shoppers')}
              </button>
            </div>
          )}
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar - Chat rooms list */}
          {userRole === 'admin' && (
            <div className="w-1/4 border-r bg-white overflow-y-auto flex flex-col">
              <div className="p-4 border-b space-y-3">
                {/* Search Input */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder={t('chat.search_placeholder', { type: activeTab === 'farmers' ? t('chat.farmers').toLowerCase() : t('chat.shoppers').toLowerCase() })}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>

                {/* Broadcast Button */}
                <Button
                  onClick={() => {
                    setBroadcastType(activeTab === 'farmers' ? 'farmer' : 'shopper');
                    setShowBroadcastModal(true);
                  }}
                  className="w-full py-2 bg-green-500 hover:bg-green-600 text-white rounded-md"
                >
                  {t('chat.message_to_all', { type: activeTab === 'farmers' ? t('chat.farmers').toLowerCase() : t('chat.shoppers').toLowerCase() })}
                </Button>
              </div>

              <div className="flex-1 overflow-y-auto p-4">
                {activeTab === 'farmers' ? (
                  <div className="space-y-6">
                    {/* Section 1: Active Chats (Original) */}
                    {farmerRooms.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-green-500 mb-4">
                          {fcount} {t('chat.active_farmer_chats')}
                        </h4>
                        <ul className="space-y-3">
                          {farmerRooms.map((room) => (
                            <li
                              key={room.room_id}
                              className={`flex items-center p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
                                room.room_id.toString() === roomId ? 'bg-gray-100' : ''
                              }`}
                              onClick={() => navigate(`${location.pathname}?roomId=${room.room_id}`)}
                            >
                              <div className="w-10 h-10 rounded-full overflow-hidden mr-3 flex-shrink-0">
                                {room.image ? (
                                  <img
                                    src={`http://localhost:5432/public/images/users/${room.image}`}
                                    alt={room.name || 'Farmer'}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-green-500 text-white flex items-center justify-center font-bold">
                                    {(room.name?.[0] || 'F').toUpperCase()}
                                  </div>
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <span className="block text-sm font-medium text-gray-900 truncate">
                                  {room.name || `Farmer #${room.room_id}`}
                                </span>
                                <span className="block text-xs text-gray-500 truncate">
                                  {room.last_message || t('chat.no_messages_yet')}
                                </span>
                              </div>
                              {room.unread_count > 0 && (
                                <span className="ml-2 bg-green-500 text-white text-xs font-medium px-2 py-1 rounded-full">
                                  {room.unread_count}
                                </span>
                              )}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Section 2: All Farmers (New) */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-sm font-semibold text-blue-500">
                          {t('chat.all_farmers')} ({getFilteredUsers(allFarmers).length})
                          {searchQuery && ` - ${t('chat.filtered_from')} ${allFarmers.length}`}
                        </h4>
                        {searchQuery && (
                          <button
                            onClick={() => setSearchQuery('')}
                            className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
                          >
                            {t('chat.clear_search')}
                          </button>
                        )}
                      </div>
                      {getFilteredUsers(allFarmers).length > 0 ? (
                        <ul className="space-y-3">
                          {getFilteredUsers(allFarmers).map((farmer) => {
                            // Find if there's an existing chat room with this farmer
                            const existingRoom = farmerRooms.find(room =>
                              room.name === `${farmer.U_FirstName} ${farmer.U_LastName}` ||
                              room.user_id === farmer.u_Id
                            );

                            return (
                              <li
                                key={farmer.u_Id}
                                className={`flex items-center p-3 rounded-lg cursor-pointer hover:bg-blue-50 transition-colors ${
                                  existingRoom && existingRoom.room_id.toString() === roomId ? 'bg-blue-100' : ''
                                }`}
                                onClick={() => {
                                  if (existingRoom) {
                                    navigate(`${location.pathname}?roomId=${existingRoom.room_id}`);
                                  } else {
                                    startChatWithUser(farmer);
                                  }
                                }}
                              >
                                <div className="w-10 h-10 rounded-full overflow-hidden mr-3 flex-shrink-0">
                                  {farmer.image ? (
                                    <img
                                      src={`http://localhost:5432/public/images/users/${farmer.image}`}
                                      alt={`${farmer.U_FirstName} ${farmer.U_LastName}`}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-blue-500 text-white flex items-center justify-center font-bold">
                                      {farmer.U_FirstName?.[0]?.toUpperCase() || 'F'}
                                    </div>
                                  )}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <span className="block text-sm font-medium text-gray-900 truncate">
                                    {farmer.U_FirstName} {farmer.U_LastName}
                                  </span>
                                  <span className="block text-xs text-gray-500 truncate">
                                    {farmer.U_Email}
                                  </span>
                                </div>
                                {!existingRoom && (
                                  <span className="ml-2 text-xs text-blue-500 font-medium">
                                    {t('chat.start_chat')}
                                  </span>
                                )}
                                {existingRoom && (
                                  <span className="ml-2 text-xs text-green-500 font-medium">
                                    Active
                                  </span>
                                )}
                              </li>
                            );
                          })}
                        </ul>
                      ) : (
                        <p className="text-center text-gray-500 py-8">No farmers found</p>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Section 1: Active Chats (Original) */}
                    {shopperRooms.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-purple-500 mb-4">
                          {scount} {t('chat.active_shopper_chats')}
                        </h4>
                        <ul className="space-y-3">
                          {shopperRooms.map((room) => (
                            <li
                              key={room.room_id}
                              className={`flex items-center p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
                                room.room_id.toString() === roomId ? 'bg-gray-100' : ''
                              }`}
                              onClick={() => navigate(`${location.pathname}?roomId=${room.room_id}`)}
                            >
                              <div className="w-10 h-10 rounded-full overflow-hidden mr-3 flex-shrink-0">
                                {room.image ? (
                                  <img
                                    src={`http://localhost:5432/public/images/users/${room.image}`}
                                    alt={room.name || 'Shopper'}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-purple-500 text-white flex items-center justify-center font-bold">
                                    {(room.name?.[0] || 'S').toUpperCase()}
                                  </div>
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <span className="block text-sm font-medium text-gray-900 truncate">
                                  {room.name || `Shopper #${room.room_id}`}
                                </span>
                                <span className="block text-xs text-gray-500 truncate">
                                  {room.last_message || t('chat.no_messages_yet')}
                                </span>
                              </div>
                              {room.unread_count > 0 && (
                                <span className="ml-2 bg-purple-500 text-white text-xs font-medium px-2 py-1 rounded-full">
                                  {room.unread_count}
                                </span>
                              )}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Section 2: All Shoppers (New) */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-sm font-semibold text-orange-500">
                          {t('chat.all_shoppers')} ({getFilteredUsers(allShoppers).length})
                          {searchQuery && ` - ${t('chat.filtered_from')} ${allShoppers.length}`}
                        </h4>
                        {searchQuery && (
                          <button
                            onClick={() => setSearchQuery('')}
                            className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-md transition-colors"
                          >
                            {t('chat.clear_search')}
                          </button>
                        )}
                      </div>
                      {getFilteredUsers(allShoppers).length > 0 ? (
                        <ul className="space-y-3">
                          {getFilteredUsers(allShoppers).map((shopper) => {
                            // Find if there's an existing chat room with this shopper
                            const existingRoom = shopperRooms.find(room =>
                              room.name === `${shopper.U_FirstName} ${shopper.U_LastName}` ||
                              room.user_id === shopper.u_Id
                            );

                            return (
                              <li
                                key={shopper.u_Id}
                                className={`flex items-center p-3 rounded-lg cursor-pointer hover:bg-orange-50 transition-colors ${
                                  existingRoom && existingRoom.room_id.toString() === roomId ? 'bg-orange-100' : ''
                                }`}
                                onClick={() => {
                                  if (existingRoom) {
                                    navigate(`${location.pathname}?roomId=${existingRoom.room_id}`);
                                  } else {
                                    startChatWithUser(shopper);
                                  }
                                }}
                              >
                                <div className="w-10 h-10 rounded-full overflow-hidden mr-3 flex-shrink-0">
                                  {shopper.image ? (
                                    <img
                                      src={`http://localhost:5432/public/images/users/${shopper.image}`}
                                      alt={`${shopper.U_FirstName} ${shopper.U_LastName}`}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-orange-500 text-white flex items-center justify-center font-bold">
                                      {shopper.U_FirstName?.[0]?.toUpperCase() || 'S'}
                                    </div>
                                  )}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <span className="block text-sm font-medium text-gray-900 truncate">
                                    {shopper.U_FirstName} {shopper.U_LastName}
                                  </span>
                                  <span className="block text-xs text-gray-500 truncate">
                                    {shopper.U_Email}
                                  </span>
                                </div>
                                {!existingRoom && (
                                  <span className="ml-2 text-xs text-orange-500 font-medium">
                                    {t('chat.start_chat')}
                                  </span>
                                )}
                                {existingRoom && (
                                  <span className="ml-2 text-xs text-purple-500 font-medium">
                                    Active
                                  </span>
                                )}
                              </li>
                            );
                          })}
                        </ul>
                      ) : (
                        <p className="text-center text-gray-500 py-8">No shoppers found</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Main chat area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {roomId ? (
              <>
                {/* Chat header */}
                <div className="p-4 bg-white border-b">
                  <div className="flex justify-between items-center">
                    <div className="flex gap-1">
                    <div className="mr-2 overflow-hidden w-10 h-10 rounded-full flex-shrink-0">
                      {currentRoom?.image ? (
                        <img
                          src={`http://localhost:5432/public/images/users/${currentRoom.image}`}
                          alt={currentRoom.user_name || 'User'}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-green-500 text-gray-200 flex items-center justify-center font-bold">
                          {(currentRoom?.name?.[0] || 'U').toUpperCase()}
                        </div>
                      )}
                    </div>
                    <div>
                      <h2 className="font-medium">{currentRoom?.name || t('chat.chat_room')}</h2>
                      <p className="text-sm text-gray-500">
                        {userRole === 'admin'
                          ? (activeTab === 'farmers' ? t('chat.chatting_with_farmer') : t('chat.chatting_with_shopper'))
                          : t('chat.chatting_with_admin')}
                      </p>
                    </div>

                    {/* Language Toggle Button */}
                    <div className="relative" ref={langDropdownRef}>
                      <button
                        onClick={() => setIsLangOpen(!isLangOpen)}
                        className="flex items-center gap-2 px-4 py-2 text-white bg-primary hover:bg-primary/90 rounded-sm transition-all duration-300 shadow-lg hover:shadow-xl border border-white/20 font-sans"
                        title="Language / ژبه"
                      >
                        <Globe size={20} className="text-white" />
                        <span className="text-sm font-medium">
                          {language === 'en' ? 'EN' : 'PS'}
                        </span>
                        <ChevronDown size={16} className={`text-white transition-transform duration-300 ${isLangOpen ? 'rotate-180' : ''}`} />
                      </button>

                      {/* Language Dropdown */}
                      {isLangOpen && (
                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden">
                          {['en', 'ps'].map((lang) => (
                            <button
                              key={lang}
                              onClick={() => {
                                toggleLanguage();
                                setIsLangOpen(false);
                              }}
                              className={`block w-full text-left px-4 py-3 transition-all duration-200 ${
                                lang === language
                                  ? 'bg-primary text-white'
                                  : 'text-gray-900 hover:bg-gray-100'
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Globe size={18} className={lang === language ? 'text-white' : 'text-primary'} />
                                  <span className="font-semibold text-sm">
                                    {lang === 'en' ? 'English' : 'پښتو'}
                                  </span>
                                </div>
                                {lang === language && (
                                  <span className="text-white text-sm font-bold">✓</span>
                                )}
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Messages area */}
                <div className="flex-1 overflow-y-auto p-4" ref={scrollRef}>
                  {messages.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full">
                      <div className="text-4xl mb-4">💬</div>
                      <p className="text-gray-500">{t('chat.no_messages_start')}</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((msg) => {
                        const isCurrentUser = msg.sender_id === userId;

                        return (
                          <div
                            key={msg.message_id}
                            className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                          >
                            {!isCurrentUser && (
                              <div className="w-8 h-8 rounded-full overflow-hidden mr-2 flex-shrink-0">
                                {msg.image ? (
                                  <img
                                    src={`http://localhost:5432/public/images/users/${msg.image}`}
                                    alt={msg.U_FirstName || 'User'}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-green-500 text-white flex items-center justify-center font-bold text-xs">
                                    {(msg.U_FirstName?.[0] || 'U').toUpperCase()}
                                  </div>
                                )}
                              </div>
                            )}

                            <div className="relative group">
                              <div
                                className={`max-w-[70%] ${isCurrentUser ? 'bg-green-500 text-white' : 'bg-gray-100 text-gray-800'} rounded-lg px-4 py-2 relative`}
                              >
                                {/* Show if message is deleted */}
                                {msg.is_deleted ? (
                                  <p className="italic text-gray-500">This message was deleted</p>
                                ) : (
                                  <>
                                    {(msg.message_type === 'image' || msg.message_type === 'both') && msg.image_url && (
                                      <div className="mb-2">
                                        <img
                                          src={`http://localhost:5432/public/images/chats/${msg.image_url}`}
                                          alt="Chat image"
                                          className="rounded-md max-w-full"
                                        />
                                      </div>
                                    )}

                                    {(msg.message_type === 'text' || msg.message_type === 'both') && msg.message_content && (
                                      <>
                                        {editingMessageId === msg.message_id ? (
                                          <div className="space-y-2">
                                            <textarea
                                              value={editingContent}
                                              onChange={(e) => setEditingContent(e.target.value)}
                                              className="w-full p-2 text-gray-800 border rounded resize-none"
                                              rows="2"
                                              autoFocus
                                            />
                                            <div className="flex gap-2">
                                              <button
                                                onClick={saveEdit}
                                                className="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                                              >
                                                Save
                                              </button>
                                              <button
                                                onClick={cancelEdit}
                                                className="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700"
                                              >
                                                Cancel
                                              </button>
                                            </div>
                                          </div>
                                        ) : (
                                          <p>
                                            {msg.message_content}
                                            {msg.is_edited && (
                                              <span className={`text-xs ml-2 ${isCurrentUser ? 'text-green-200' : 'text-gray-400'}`}>
                                                (edited)
                                              </span>
                                            )}
                                          </p>
                                        )}
                                      </>
                                    )}
                                  </>
                                )}

                                <span
                                  className={`text-xs ${isCurrentUser ? 'text-green-100' : 'text-gray-500'} block mt-1`}
                                >
                                  {new Date(msg.sent_at).toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}
                                </span>
                              </div>

                              {/* Edit and Delete buttons - only show for current user's messages */}
                              {isCurrentUser && !msg.is_deleted && editingMessageId !== msg.message_id && (
                                <div className="absolute top-0 right-0 -mt-2 -mr-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <div className="flex gap-1">
                                    <button
                                      onClick={() => startEditMessage(msg)}
                                      className="w-6 h-6 bg-blue-500 text-white rounded-full text-xs hover:bg-blue-600 flex items-center justify-center"
                                      title="Edit message"
                                    >
                                      ✏️
                                    </button>
                                    <button
                                      onClick={() => confirmDelete(msg)}
                                      className="w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 flex items-center justify-center"
                                      title="Delete message"
                                    >
                                      🗑️
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                {/* Image preview */}
                {imagePreview && (
                  <div className="p-2 border-t bg-gray-50">
                    <div className="relative inline-block">
                      <img src={imagePreview || '/placeholder.svg'} alt="Preview" className="h-20 w-auto rounded-md" />
                      <button
                        className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center -mt-2 -mr-2"
                        onClick={clearImage}
                      >
                        &times;
                      </button>
                    </div>
                  </div>
                )}

                {/* Message input */}
                <div className="p-4 border-t bg-white flex items-center gap-2">
                  <button
                    className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors"
                    onClick={() => document.getElementById('image-upload').click()}
                  >
                    📷
                    <input
                      id="image-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleImageChange}
                    />
                  </button>

                  <input
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder="Type your message..."
                    disabled={loading}
                    className="flex-1 p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  />

                  <Button
                    onClick={sendMessage}
                    disabled={loading || (!message.trim() && !selectedImage)}
                    className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t('chat.send')}
                  </Button>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                <div className="text-6xl mb-4">💬</div>
                <h2 className="text-2xl font-semibold mb-2">{t('chat.welcome_to_chat')}</h2>
                <p className="text-gray-600 dark:text-muted mb-8 max-w-md">
                  {userRole === 'admin'
                    ? t('chat.select_conversation')
                    : t('chat.start_conversation')}
                </p>

                {userRole !== 'admin' && (
                  <Button
                    onClick={openAdminSelectModal}
                    className="px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
                  >
                    {t('chat.start_chat_with_admin')}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Broadcast Modal */}
      <Modal
        isOpen={showBroadcastModal}
        title={`Broadcast to all ${broadcastType}s`}
        onClose={() => setShowBroadcastModal(false)}
      >
        <div className="space-y-4">
          <textarea
            value={broadcastMessage}
            onChange={(e) => setBroadcastMessage(e.target.value)}
            placeholder={t('chat.broadcast_placeholder', { type: broadcastType === 'farmer' ? t('chat.farmer') : t('chat.shopper') })}
            className="w-full p-3 border rounded-md h-32 focus:outline-none focus:ring-2 focus:ring-green-500"
          />
          <div className="flex justify-end gap-2">
            <Button
              onClick={() => setShowBroadcastModal(false)}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md"
            >
              {t('chat.cancel')}
            </Button>
            <Button
              onClick={sendBroadcastMessage}
              disabled={!broadcastMessage.trim() || loading}
              className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('chat.send_to_all', { type: broadcastType === 'farmer' ? t('chat.farmer') : t('chat.shopper') })}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Admin Selection Modal */}
      <Modal
        isOpen={showAdminSelectModal}
        title="Select an Admin to Chat With"
        onClose={() => setShowAdminSelectModal(false)}
      >
        <div className="space-y-4">
          {loadingAdmins ? (
            <div className="flex flex-col items-center justify-center py-8">
              <LoadingSpinner size="small" />
              <p className="mt-4 text-gray-500">Loading admins...</p>
            </div>
          ) : admins.length > 0 ? (
            <>
              {adminFetchError && (
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <span className="text-yellow-400">⚠️</span>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">{adminFetchError}</p>
                    </div>
                  </div>
                </div>
              )}
              <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-1">
                {admins.map((admin) => (
                  <div
                    key={admin.u_Id}
                    className="flex items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-all border border-gray-200"
                  >
                    <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 mr-4">
                      {admin.image && admin.image !== 'default-user.jpg' ? (
                        <img
                          src={`http://localhost:5432/public/images/users/${admin.image}`}
                          alt={`${admin.U_FirstName} ${admin.U_LastName}`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-green-500 text-white flex items-center justify-center font-bold text-lg">
                          {admin.U_FirstName ? admin.U_FirstName[0].toUpperCase() : 'A'}
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{`${admin.U_FirstName} ${admin.U_LastName}`}</h3>
                      <p className="text-sm text-gray-600">{admin.EmailVerified ? 'Verified Admin' : 'Admin'}</p>
                      <p className="text-xs text-gray-400">{admin.U_Email}</p>
                      {admin.address && <p className="text-xs text-gray-500 mt-1">Location: {admin.address}</p>}
                    </div>
                    <Button
                      onClick={() => startChat(admin.u_Id)}
                      className="ml-4 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
                    >
                      Chat Now
                    </Button>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="py-8 text-center text-gray-500">
              <p>No admins available at the moment.</p>
            </div>
          )}
          <div className="pt-4 border-t flex justify-end">
            <Button
              onClick={() => setShowAdminSelectModal(false)}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md"
            >
              Cancel
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        title="Delete Message"
        onClose={() => setShowDeleteModal(false)}
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to delete this message? This action cannot be undone.
          </p>
          {messageToDelete && (
            <div className="bg-gray-50 p-3 rounded border-l-4 border-gray-300">
              <p className="text-sm text-gray-700">"{messageToDelete.message_content}"</p>
            </div>
          )}
          <div className="flex justify-end gap-2">
            <Button
              onClick={() => setShowDeleteModal(false)}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md"
            >
              Cancel
            </Button>
            <Button
              onClick={deleteMessage}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md"
            >
              Delete
            </Button>
          </div>
        </div>
      </Modal>
    </ErrorBoundary>
  );
}

export default Chat;
