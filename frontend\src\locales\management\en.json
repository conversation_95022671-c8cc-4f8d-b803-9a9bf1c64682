{"sidebar": {"main": "Main", "chickens": "Chickens", "system": "System", "logout": "Logout", "dashboard": "Dashboard", "users": "Users", "news": "News", "services": "Services", "farms": "Farms", "shops": "Shops", "seeds": "Seeds", "drugs": "Drugs", "feed": "Feed", "medicine": "Medicine", "inventory": "Inventory", "notifications": "Notifications", "reports": "Reports", "settings": "Settings", "profile": "Profile", "english": "English", "pashto": "Pashto", "new": "New", "chat": "Cha<PERSON>"}, "farmer": {"dashboard": "Farmer Dashboard", "welcome": "Welcome", "weather_forecast": "Weather Forecast", "chat_with_support": "Chat with Support", "full_chat": "Full Chat", "loading_weather": "Loading weather data...", "weather_not_available": "Weather data not available.", "check_weather_and_chat": "Check the weather and chat with support.", "support": "Support", "hello_help": "Hello! How can we help you today?", "question_irrigation": "I have a question about irrigation.", "you": "You", "minutes_ago": "minutes ago", "just_now": "Just now", "home": "Home", "settings": "Settings", "notifications": "Notifications", "switch_to_pashto": "Switch to Pashto", "switch_to_english": "Switch to English", "toggle_dark_mode": "Toggle Dark Mode", "open_menu": "Open menu", "close_menu": "Close menu"}, "chat": {"farmers": "Farmers", "shoppers": "Shoppers", "search_placeholder": "Search {type}...", "message_to_all": "Message to all {type}", "active_farmer_chats": "Active Farmer Chats", "active_shopper_chats": "Active Shopper Chats", "all_farmers": "All Farmers", "all_shoppers": "All Shoppers", "no_messages_yet": "No messages yet", "start_chat": "Start Chat", "clear_search": "Clear Search", "filtered_from": "filtered from", "no_messages_start": "No messages yet. Start the conversation!", "welcome_to_chat": "Welcome to Chat", "select_conversation": "Select a conversation from the sidebar to start chatting", "start_conversation": "Start a conversation with an admin for support and inquiries", "start_chat_with_admin": "Start Chat with <PERSON><PERSON>", "send": "Send", "send_to_all": "Send to all {type}s", "broadcast_placeholder": "Type your message to all {type}s...", "cancel": "Cancel", "chatting_with_farmer": "Chatting with <PERSON>", "chatting_with_shopper": "Chatting with <PERSON><PERSON>", "chatting_with_admin": "Chatting with <PERSON><PERSON>", "chat_room": "Chat Room", "farmer": "farmer", "shopper": "shopper", "broadcast_to_all": "Broadcast to all {type}", "back": "Back", "active": "Active", "chat_now": "Chat Now", "verified_admin": "Verified <PERSON><PERSON>", "admin": "Admin", "location": "Location", "no_admins_available": "No admins available at the moment.", "failed_to_load_chat_rooms": "Failed to load chat rooms", "failed_to_load_messages": "Failed to load messages", "failed_to_send_message": "Failed to send message", "failed_to_edit_message": "Failed to edit message", "failed_to_delete_message": "Failed to delete message", "failed_to_load_admin_list": "Failed to load admin list. Please try again.", "error": "Error", "loading": "Loading", "loading_admins": "Loading admins...", "try_again": "Try Again", "type_your_message": "Type your message...", "edit": "Edit", "delete": "Delete", "save": "Save", "edited": "(edited)", "this_message_was_deleted": "This message was deleted", "today": "Today", "yesterday": "Yesterday", "minutes_ago": "minutes ago", "hours_ago": "hours ago", "days_ago": "days ago", "select_admin_to_chat": "Select an Admin to Chat With", "delete_message": "Delete Message", "delete_message_confirmation": "Are you sure you want to delete this message? This action cannot be undone.", "edit_message": "Edit message", "delete_message_tooltip": "Delete message", "view_all_notifications": "View All Notifications", "notifications": "Notifications", "mark_all_as_read": "<PERSON> as <PERSON>", "no_notifications": "No notifications yet", "new_message_from": "New message from", "failed_to_start_chat": "Failed to start chat", "could_not_load_admins": "Could not load admins from server. Using backup data.", "could_not_connect": "Could not connect to server. Using backup data.", "no_farmers_found": "No farmers found", "no_shoppers_found": "No shoppers found"}, "auth": {"login": {"title": "Welcome Back", "description": "Sign in to access your account", "email": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot your password?", "button": "Sign In", "loading": "Signing in...", "noAccount": "Don't have an account?", "signup": "Sign up"}, "register": {"title": "Create Account", "description": "Join us today and get started", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "role": "Role", "address": "Address", "phone": "Phone Number", "button": "Create Account", "loading": "Creating account...", "hasAccount": "Already have an account?", "signin": "Sign in"}}, "dashboard": {"welcome": "Welcome back", "overview": "Dashboard Overview", "welcome_message": "Welcome back! Here's what's happening with your farm management system.", "quick_links": "Quick Links", "totalUsers": "Total Users", "totalFarms": "Total Farms", "totalShops": "Total Shops", "totalChickens": "Total Chickens", "totalServices": "Total Services", "totalNews": "Total News", "totalDrugs": "Total Drugs", "totalFeed": "Total Feed", "totalSeeds": "Total Seeds", "totalInquiries": "Total Inquiries", "users": "Users", "news": "News", "services": "Services", "farms": "Farms", "shops": "Shops", "chickens": "Chickens", "seeds": "Seeds", "drugs": "Drugs", "feed": "Feed", "inquiries": "Inquiries", "reports": "Reports", "settings": "Settings", "daily_feed_consumption": "Daily Feed Consumption", "dashboard_overview_chart": "Dashboard Overview", "real_time_summary": "Real-Time Summary", "recentActivity": "Recent Activity", "users_news_services": "Users, News & Services", "chickens_drugs_feed": "Chickens, Drugs & Feed", "shops_farms": "Shops & Farms", "current": "Current", "last_updated": "Last updated"}, "users": {"title": "User Management", "manage_users": "Manage Users", "add_user": "Add User", "edit_user": "Edit User", "user_list": "User List", "search_placeholder": "Search users by name, email, or role...", "user": "User", "role": "Role", "status": "Status", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "previous": "Previous", "next": "Next", "confirm_delete": "Confirm Delete", "confirm_delete_message": "Are you sure you want to delete \"{name}\"? This action cannot be undone.", "cancel": "Cancel", "user_details": "User Details", "address": "Address", "email_verified": "<PERSON><PERSON>", "yes": "Yes", "no": "No", "close": "Close", "showing_results": "Showing {from} to {to} of {total} results", "save_user": "Save User", "saving": "Saving...", "profile_picture": "Profile Picture", "first_name": "First Name", "last_name": "Last Name", "email_address": "Email Address", "password": "Password", "confirm_password": "Confirm Password", "admin": "Admin", "farmer": "<PERSON>", "shopper": "Shopper", "active": "Active", "inactive": "Inactive", "validation": {"first_name_required": "First name is required", "last_name_required": "Last name is required", "email_required": "Email is required", "password_required": "Password is required", "invalid_email": "Invalid email format", "password_min_length": "Password must be at least 6 characters", "passwords_not_match": "Passwords do not match"}, "messages": {"user_added_successfully": "User added successfully!", "user_updated_successfully": "User updated successfully!", "user_deleted_successfully": "User deleted successfully!", "failed_to_add_user": "Failed to add user. Please try again.", "failed_to_update_user": "Failed to update user. Please try again.", "failed_to_delete_user": "Failed to delete user. Please try again."}}, "news": {"title": "News Management", "manage_news": "Manage News", "add_news": "Add News", "edit_news": "Edit News", "news_list": "News List", "add_new_news": "Add New News", "search_placeholder": "Search news...", "title_field": "Title", "content": "Content", "category": "Category", "status": "Status", "author": "Author", "excerpt": "Excerpt", "featured": "Featured", "image": "Image", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "back": "Back", "save": "Save", "saving": "Saving...", "published": "Published", "draft": "Draft", "no_news_found": "No news found", "confirm_delete_news": "Are you sure you want to delete this news item?", "validation": {"title_required": "Title is required", "title_min_length": "Title must be at least 5 characters", "content_required": "Content is required", "content_min_length": "Content must be at least 8 characters", "excerpt_required": "Excerpt is required", "category_required": "Category is required", "author_required": "Author is required"}, "messages": {"news_added_successfully": "News added successfully!", "news_updated_successfully": "News updated successfully!", "news_deleted_successfully": "News deleted successfully!", "error_loading_news": "Error loading news. Please try again.", "error_saving_news": "Error saving news. Please try again."}}, "services": {"title": "Services Management", "manage_services": "Manage Services", "add_service": "Add Service", "edit_service": "Edit Service", "service_list": "Service List", "search_placeholder": "Search services...", "service_name": "Service Name", "description": "Description", "price": "Price", "category": "Category", "status": "Status", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "save": "Save", "saving": "Saving...", "deleting": "Deleting...", "cancel": "Cancel", "active": "Active", "inactive": "Inactive", "no_services_found": "No services found", "confirm_delete": "Are you sure you want to delete this service?", "confirm_delete_title": "Confirm Delete", "all_categories": "All Categories", "filter_by_category": "Filter by category:", "previous": "Previous", "next": "Next", "enter_service_title": "Enter service title", "select_category": "Select a category", "enter_price": "Enter price (e.g., 450)", "categories": {"premium_hens": "Premium Hens", "baby_chickens": "Baby Chickens", "wholesale": "Wholesale"}, "validation": {"name_required": "Service name is required", "description_required": "Description is required", "price_required": "Price is required", "category_required": "Category is required"}, "messages": {"service_added_successfully": "Service added successfully!", "service_updated_successfully": "Service updated successfully!", "service_deleted_successfully": "Service deleted successfully!", "error_loading_services": "Error loading services. Please try again.", "error_saving_service": "Error saving service. Please try again."}}, "shops": {"title": "Shop Management", "shop_management": "Shop Management", "manage_shops_description": "Manage and monitor all shops in the system", "add_new_shop": "Add New Shop", "edit_shop": "Edit Shop", "shop_list": "Shop List", "search_shops_by_name": "Search shops by name...", "total_shops": "Total Shops", "unique_owners": "Unique Owners", "avg_shops_per_owner": "Avg Shops/Owner", "shop_distribution_by_owner": "Shop Distribution by Owner", "top_shop_owners": "Top 10 shop owners by number of shops", "search_and_filter_shops": "Search & Filter Shops", "sort_by": "Sort By", "name": "Name", "owner": "Owner", "email": "Email", "phone": "Phone", "location": "Location", "actions": "Actions", "edit": "Edit", "delete": "Delete", "clear_all": "Clear All", "no_shops_found": "No shops found", "confirm_delete_shop": "Are you sure you want to delete this shop?", "loading_shops": "Loading shops...", "no_shop_data": "No shop data available", "validation": {"name_required": "Shop name is required", "owner_required": "Owner name is required", "email_required": "Email is required", "phone_required": "Phone is required", "location_required": "Location is required"}, "messages": {"shop_added_successfully": "Shop added successfully!", "shop_updated_successfully": "Shop updated successfully!", "shop_deleted_successfully": "Shop deleted successfully!", "error_loading_shops": "Error loading shops. Please try again.", "error_saving_shop": "Error saving shop. Please try again."}}, "drugs": {"title": "Drugs Management", "description": "Purchase drugs and transfer to farms for chicken care", "purchases_button": "Purchases", "transfers_button": "Transfers", "purchase_drugs_button": "Purchase Drugs", "manage": "Manage", "analyze": "Analyze", "quick_actions": "Quick Actions", "purchase_new_drugs": "Purchase New Drugs", "transfer_to_farm": "Transfer to Farm", "purchases": {"title": "Drug Purchases", "description": "Purchase drugs from suppliers", "add_purchase": "Purchase Drugs", "edit_purchase": "Edit Drug Purchase", "search_placeholder": "Search purchases...", "drug_name": "Drug Name", "supplier": "Supplier", "quantity": "Quantity", "unit_price": "Unit Price", "total_amount": "Total Amount", "purchase_date": "Purchase Date", "expiry_date": "Expiry Date", "batch_number": "Batch Number", "notes": "Notes", "no_purchases_found": "No purchases found", "back_to_drugs": "Back to Drugs", "manage_inventory": "Manage drug purchases and inventory", "refresh": "Refresh", "purchase_history": "Purchase History", "purchase_overview": "Purchase Overview", "transferred": "Transferred", "remaining": "Remaining", "price_per_unit": "Price/Unit", "actions": "Actions", "edit_button": "Edit", "delete_button": "Delete", "completed": "Completed", "available": "Available", "remaining_percentage": "% remaining", "delete_purchase": "Delete Purchase", "delete_confirmation": "This action cannot be undone", "delete_warning": "Are you sure you want to delete the purchase for", "delete_permanent": "This will permanently remove the purchase record from the system.", "cancel": "Cancel", "delete_purchase_button": "Delete Purchase", "retry": "Retry", "validation": {"drug_name_required": "Drug name is required", "supplier_required": "Supplier is required", "quantity_required": "Quantity is required", "unit_price_required": "Unit price is required", "purchase_date_required": "Purchase date is required"}, "messages": {"purchase_created_successfully": "Drug purchase created successfully!", "purchase_updated_successfully": "Drug purchase updated successfully!", "purchase_deleted_successfully": "Drug purchase deleted successfully!", "error_creating_purchase": "Error creating drug purchase", "error_updating_purchase": "Error updating drug purchase", "operation_completed": "Operation completed successfully!"}, "add": {"title": "Purchase Drugs", "description": "Add a new drug purchase order", "form_title": "Purchase Information", "drug_name_label": "Drug Name *", "drug_name_placeholder": "Enter drug name", "quantity_label": "Quantity *", "quantity_placeholder": "Enter quantity", "price_per_unit_label": "Price per Unit ($) *", "price_per_unit_placeholder": "Enter price per unit", "total_amount_label": "Total Amount ($)", "supplier_label": "Supplier *", "supplier_placeholder": "Enter supplier name", "purchase_date_label": "Purchase Date *", "notes_label": "Notes", "notes_placeholder": "Enter any additional notes", "save_button": "Save Purchase", "saving_button": "Saving...", "validation": {"drug_name_required": "Drug name is required", "quantity_required": "Quantity must be at least 1", "price_required": "Price per unit must be a positive number", "supplier_required": "Supplier is required", "purchase_date_required": "Purchase date is required"}, "error_message": "Failed to add purchase. Please try again."}, "edit": {"title": "Edit Drug Purchase", "description": "Update drug purchase information", "form_title": "Edit Purchase Information", "save_button": "Save Changes", "saving_button": "Saving...", "validation": {"drug_name_required": "Drug name is required", "quantity_required": "Quantity must be at least 1", "price_required": "Price per unit must be a positive number", "supplier_required": "Supplier is required", "purchase_date_required": "Purchase date is required"}, "error_message": "Failed to update purchase. Please try again.", "loading_error": "Failed to load purchase data. Please try again."}}, "transfers": {"title": "Drug Transfers", "description": "Transfer drugs to farms for chicken care", "back_to_drugs": "Back to Drugs", "back_to_transfers": "Back to Transfers", "add_transfer": "Transfer Drugs", "edit_transfer": "Edit Drug Transfer", "refresh": "Refresh", "search_placeholder": "Search transfers...", "total_profit_loss": "Total Profit/Loss", "total_profit": "Total profit", "total_loss": "Total loss", "break_even": "Break even", "transfer_distribution_by_farm": "Transfer Distribution by Farm", "quantity_transferred": "Quantity Transferred", "transfer_history": "Transfer History", "drug_name": "Drug Name", "quantity": "Quantity", "farm_name": "Farm Name", "location": "Location", "transfer_date": "Transfer Date", "price_afn": "Price (AFN)", "profit_loss": "Profit/Loss", "status": "Status", "actions": "Actions", "edit_button": "Edit", "delete_button": "Delete", "delete_transfer": "Delete Transfer", "delete_confirmation": "This action cannot be undone", "delete_warning": "Are you sure you want to delete the transfer", "delete_permanent": "This will permanently remove the transfer record from the system.", "cancel": "Cancel", "delete_transfer_button": "Delete Transfer", "select_purchase": "Select Purchase", "select_farm": "Select Farm", "transfer_quantity": "Transfer Quantity", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "no_transfers_found": "No drug transfers found", "add": {"title": "Transfer Drugs", "description": "Transfer drugs to farm", "card_title": "Select Drug Purchase and Farm", "drug_purchase": "Drug Purchase", "select_drug_purchase": "Select a drug purchase...", "no_purchases_available": "No drug purchases available.", "need_create_purchases": "You need to create purchases first.", "click_create_purchase": "Click here to create a purchase", "no_available_purchases": "No available drug purchases.", "all_transferred": "All purchases have been fully transferred to farms.", "create_new_purchase": "Create a new purchase", "to_continue_transferring": "to continue transferring drugs.", "drug": "Drug", "supplier": "Supplier", "total_quantity": "Total Quantity", "transferred": "Transferred", "remaining": "Remaining", "completed": "COMPLETED", "purchase_date": "Purchase Date", "farm": "Farm", "select_farm": "Select a farm...", "no_farms_available": "No farms available", "need_create_farms": "You need to create farms first.", "click_create_farm": "Click here to create a farm", "owner": "Owner", "email": "Email", "phone": "Phone", "quantity": "Quantity", "max": "Max", "enter_quantity": "Enter quantity", "maximum_available": "Maximum available", "price_per_unit": "Price Per Unit (AFN)", "enter_price_per_unit": "Enter transfer price per unit", "purchase_price": "Purchase price", "per_unit": "per unit", "total_price": "Total Price (AFN)", "auto_calculated": "Auto-calculated", "auto_calculated_formula": "Automatically calculated: Quantity × Price Per Unit", "notes": "Notes", "enter_notes": "Enter any additional notes", "transfer_completed": "Transfer Completed", "save_transfer": "Save Transfer"}, "edit": {"title": "Edit Drug Transfer", "description": "Update drug transfer information", "card_title": "Edit Transfer Information", "save_changes": "Save Changes"}, "validation": {"purchase_required": "Please select a purchase", "farm_required": "Please select a farm", "quantity_required": "Transfer quantity is required", "transfer_date_required": "Transfer date is required", "quantity_exceeds_available": "Quantity exceeds available stock"}, "messages": {"transfer_created_successfully": "Drug transfer created successfully!", "transfer_updated_successfully": "Drug transfer updated successfully!", "transfer_deleted_successfully": "Drug transfer deleted successfully!", "error_creating_transfer": "Error creating drug transfer", "error_updating_transfer": "Error updating drug transfer", "error_deleting_transfer": "Failed to delete transfer. Please try again.", "operation_completed": "Operation completed successfully!"}}, "reports": {"title": "Drug Reports", "description": "Analyze profit, loss & performance", "total_purchases": "Total Purchases", "total_transfers": "Total Transfers", "total_amount": "Total Amount", "profit_analysis": "Profit Analysis", "loss_analysis": "Loss Analysis"}, "statistics": {"purchase_overview": "Purchase Overview", "transfer_overview": "Transfer Overview", "total_purchases": "Total Purchases", "purchase_orders": "Purchase orders", "total_quantity": "Total Quantity", "units_purchased": "Units purchased", "total_amount": "Total Amount", "total_spent": "Total spent", "total_transfers": "Total Transfers", "transfer_orders": "Transfer orders", "transferred_quantity": "Transferred Quantity", "units_transferred": "Units transferred", "completed": "Completed", "successful_transfers": "Successful transfers"}}, "seeds": {"title": "Seeds Management", "description": "Purchase seeds and transfer to farms for chicken care", "purchases": {"title": "Seed Purchases", "add_purchase": "Purchase Seeds", "edit_purchase": "Edit Seed Purchase", "search_placeholder": "Search seed purchases...", "seed_type": "Seed Type", "supplier": "Supplier", "quantity": "Quantity", "unit_price": "Unit Price", "total_amount": "Total Amount", "purchase_date": "Purchase Date", "expiry_date": "Expiry Date", "notes": "Notes", "no_purchases_found": "No seed purchases found", "validation": {"seed_type_required": "Seed type is required", "supplier_required": "Supplier is required", "quantity_required": "Quantity is required", "unit_price_required": "Unit price is required", "purchase_date_required": "Purchase date is required"}, "messages": {"purchase_created_successfully": "Seed purchase created successfully!", "purchase_updated_successfully": "Seed purchase updated successfully!", "purchase_deleted_successfully": "Seed purchase deleted successfully!", "error_creating_purchase": "Error creating seed purchase", "error_updating_purchase": "Error updating seed purchase"}}, "transfers": {"title": "Seed Transfers", "add_transfer": "Transfer to Farm", "edit_transfer": "Edit Seed Transfer", "search_placeholder": "Search seed transfers...", "select_purchase": "Select Purchase", "select_farm": "Select Farm", "transfer_date": "Transfer Date", "transfer_quantity": "Transfer Quantity", "status": "Status", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "no_transfers_found": "No seed transfers found", "validation": {"purchase_required": "Please select a purchase", "farm_required": "Please select a farm", "quantity_required": "Transfer quantity is required", "transfer_date_required": "Transfer date is required", "quantity_exceeds_available": "Quantity exceeds available stock"}, "messages": {"transfer_created_successfully": "Seed transfer created successfully!", "transfer_updated_successfully": "Seed transfer updated successfully!", "transfer_deleted_successfully": "Seed transfer deleted successfully!", "error_creating_transfer": "Error creating seed transfer", "error_updating_transfer": "Error updating seed transfer"}}, "reports": {"title": "Seed Reports", "description": "Analyze profit, loss & performance", "total_purchases": "Total Purchases", "total_transfers": "Total Transfers", "total_amount": "Total Amount", "profit_analysis": "Profit Analysis", "loss_analysis": "Loss Analysis"}, "statistics": {"purchase_overview": "Purchase Overview", "transfer_overview": "Transfer Overview", "total_quantity": "Total Quantity", "units_purchased": "Units purchased", "units_transferred": "Units transferred", "successful_transfers": "Successful transfers", "total_spent": "Total spent"}}, "chickens": {"title": "Chicken Management", "chicken_management": "Chicken Management", "manage_chicken_lifecycle": "Manage complete chicken lifecycle from purchase to sale", "dashboard": "Chicken Dashboard", "quick_actions": "Quick Actions", "management_sections": "Management Sections", "chicken_lifecycle_process": "Chicken Lifecycle Process", "purchase": "Purchase", "allocate": "Allocate", "buyback": "Buyback", "distribute": "Distribute", "purchase_chickens": "Purchase Chickens", "buy_chickens_from_suppliers": "Buy chickens from suppliers", "allocate_to_farm": "Allocate to Farm", "send_chickens_to_farms": "Send chickens to farms", "send_to_farms_45_days": "Send to farms for 45 days", "buyback_from_farm": "Buyback from Farm", "buy_chickens_back_from_farms": "Buy chickens back from farms", "buy_back_after_45_days": "Buy back after 45 days", "distribute_to_shops": "Distribute to Shops", "send_chickens_to_shops": "Send chickens to shops", "send_to_shops_for_sale": "Send to shops for sale", "purchases_management": "Purchases Management", "view_all_chicken_purchases": "View all chicken purchases", "farm_allocations": "Farm Allocations", "manage_farm_allocations": "Manage farm allocations", "buybacks_management": "Buybacks Management", "manage_farm_buybacks": "Manage farm buybacks", "shop_distributions": "Shop Distributions", "manage_shop_distributions": "Manage shop distributions", "process_reports": "Process Reports", "view_complete_lifecycle_profit_loss": "View complete lifecycle profit/loss", "total_purchases": "Total Purchases", "active_allocations": "Active Allocations", "total_buybacks": "Total Buybacks", "total_distributions": "Total Distributions", "chickens": "chickens", "ready_for_buyback": "Ready for Buyback", "back_to_dashboard": "Back to Dashboard", "chicken_purchases": "Chicken Purchases", "manage_chicken_purchases_description": "Manage all chicken purchases from suppliers", "add_new_purchase": "Add New Purchase", "search_purchases": "Search purchases by supplier name or notes...", "loading_purchases": "Loading purchases...", "purchases_list": "Purchases List", "date": "Date", "supplier": "Supplier", "quantity": "Quantity", "price_per_chicken": "Price/Chicken", "total_price": "Total Price", "status": "Status", "allocated": "Allocated", "available": "Available", "partial": "Partial", "remaining": "remaining", "no_purchases_found": "No purchases found", "confirm_delete_purchase": "Are you sure you want to delete this purchase?", "total_chickens": "Total Chickens", "total_amount": "Total Amount", "create_new_chicken_purchase": "Create a new chicken purchase from supplier", "purchase_information": "Purchase Information", "purchase_date": "Purchase Date", "enter_quantity": "Enter number of chickens", "enter_price_per_chicken": "Enter price per chicken", "auto_calculated": "Auto-calculated", "total_price_auto_calculated": "This field is automatically calculated", "supplier_name": "Supplier Name", "enter_supplier_name": "Enter supplier name", "supplier_contact": "Supplier Contact (Optional)", "notes": "Notes (Optional)", "enter_notes": "Enter any additional notes about this purchase", "cancel": "Cancel", "create_purchase": "Create Purchase", "creating_purchase": "Creating...", "back_to_purchases": "Back to Purchases", "purchase_date_required": "Purchase date is required", "quantity_required": "Valid quantity is required", "price_required": "Valid price per chicken is required", "supplier_name_required": "Supplier name is required", "invalid_phone_format": "Phone must be in format +93xxxxxxxxx", "purchase_created_successfully": "Purchase created successfully", "create_purchase_error": "Failed to create purchase", "manage_farm_allocations_description": "Manage chickens allocated to farms for 45 days", "back_to_allocations": "Back to Allocations", "allocate_chickens_to_farm_description": "Allocate chickens to a farm for 45 days", "allocation_information": "Allocation Information", "select_purchase": "Select Purchase", "choose_purchase": "Choose a purchase...", "select_farm": "Select Farm", "choose_farm": "Choose a farm...", "allocation_date": "Allocation Date", "allocation_timeline": "Allocation Timeline", "allocating": "Allocating...", "purchase_required": "Please select a purchase", "farm_required": "Please select a farm", "allocation_date_required": "Allocation date is required", "quantity_exceeds_available": "Quantity cannot exceed available chickens", "allocation_created_successfully": "Chickens allocated to farm successfully", "create_allocation_error": "Failed to allocate chickens to farm", "loading_data": "Loading data...", "loading_allocations": "Loading allocations...", "allocations_list": "Allocations List", "farm": "Farm", "days_elapsed": "Days Elapsed", "bought_back": "Bought Back", "in_progress": "In Progress", "completed": "Completed", "days": "days", "confirm_delete_allocation": "Are you sure you want to delete this allocation?", "no_allocations_found": "No allocations found", "search_allocations": "Search allocations by farm name, owner, or supplier...", "chicken_buybacks": "Chicken Buybacks", "manage_chicken_buybacks_description": "Manage chickens bought back from farms after 45 days", "buyback_chickens_from_farm_description": "Buy back chickens from farm after 45 days", "buyback_information": "Buyback Information", "select_allocation": "Select Allocation (Ready for Buyback)", "choose_allocation": "Choose an allocation...", "buyback_date": "Buyback Date", "days_completed": "Days Completed", "buyback_summary": "Buyback Summary", "buying_back": "Buying Back...", "allocation_required": "Please select an allocation", "buyback_date_required": "Buyback date is required", "quantity_exceeds_remaining": "Quantity cannot exceed remaining chickens", "minimum_45_days_required": "Minimum 45 days required before buyback", "buyback_created_successfully": "Chickens bought back from farm successfully", "create_buyback_error": "Failed to buy back chickens from farm", "no_ready_allocations": "No allocations available for buyback (45+ days required and remaining quantity > 0)", "owner": "Owner", "days_left": "days left", "maximum_available_for_buyback": "Maximum available for buyback", "need": "Need", "more_days": "more days", "back_to_buybacks": "Back to Buybacks", "loading_buybacks": "Loading buybacks...", "buybacks_list": "Buybacks List", "fully_distributed": "Fully Distributed", "partially_distributed": "Partially Distributed", "distributed": "Distributed", "no_buybacks_found": "No buybacks found", "confirm_delete_buyback": "Are you sure you want to delete this buyback?", "search_buybacks": "Search buybacks by farm name or owner...", "manage_shop_distributions_description": "Manage chickens distributed to shops for sale", "distribute_chickens_to_shop_description": "Distribute chickens to a shop for sale", "distribution_information": "Distribution Information", "select_buyback": "Select Buyback (Available Chickens)", "choose_buyback": "Choose a buyback...", "select_shop": "Select Shop", "choose_shop": "Choose a shop...", "distribution_date": "Distribution Date", "distribution_summary": "Distribution Summary", "distributing": "Distributing...", "buyback_required": "Please select a buyback", "shop_required": "Please select a shop", "distribution_date_required": "Distribution date is required", "distribution_created_successfully": "Chickens distributed to shop successfully", "create_distribution_error": "Failed to distribute chickens to shop", "no_available_buybacks": "No buybacks with available chickens", "email": "Email", "phone": "Phone", "shop": "Shop", "from_farm": "From Farm", "to_shop": "To Shop", "total_value": "Total Value", "maximum_available": "Maximum available", "back_to_distributions": "Back to Distributions", "loading_distributions": "Loading distributions...", "distributions_list": "Distributions List", "sold": "Sold", "no_distributions_found": "No distributions found", "confirm_delete_distribution": "Are you sure you want to delete this distribution?", "search_distributions": "Search distributions by shop name, owner, or farm...", "chicken_process_report": "Chicken Process Report", "complete_lifecycle_analysis": "Complete lifecycle analysis with profit/loss tracking", "process_completion": "Process Completion", "process_completion_description": "A process is marked as \"COMPLETED\" when chickens have gone through all 4 stages: Purchase → Farm Allocation → Buyback → Shop Distribution", "export_report": "Export Report", "completed_processes": "Completed Processes", "total_investment": "Total Investment", "net_profit": "Net Profit", "margin": "margin", "filter_by_status": "Filter by Process Status", "all_processes": "All Processes", "at_buyback": "At Buyback", "at_farm": "At Farm", "purchased_only": "Purchased Only", "profitable": "Profitable", "with_losses": "With Losses", "showing": "Showing", "of": "of", "processes": "processes", "clear_filter": "Clear filter", "process_analytics_dashboard": "Process Analytics Dashboard", "time_period": "Time Period", "daily": "Daily", "monthly": "Monthly", "yearly": "Yearly", "current_view": "Current View", "daily_breakdown": "Daily breakdown of process performance and trends", "monthly_summary": "Monthly summary with aggregated metrics and patterns", "yearly_overview": "Yearly overview showing long-term trends and growth", "data_points": "Data Points", "date_range": "Date Range", "process_status_distribution": "Process Status Distribution", "overview_of_all_process_statuses": "Overview of all process statuses and completion rates", "profit_trend_analysis": "Profit Trend Analysis", "daily_view": "Daily View", "monthly_view": "Monthly View", "yearly_view": "Yearly View", "daily_profit_trends": "Daily profit trends and investment patterns", "monthly_financial_performance": "Monthly financial performance overview", "annual_growth_trends": "Annual growth and profitability trends", "quantity_flow_analysis": "Quantity Flow Analysis", "process_flow_tracking": "Process Flow Tracking", "track_chicken_quantities": "Track chicken quantities through each stage of the process lifecycle", "daily_performance": "Daily Performance Trends", "monthly_performance": "Monthly Performance Trends", "yearly_performance": "Yearly Performance Trends", "detailed_process_analysis": "Detailed Process Analysis", "initial_qty": "Initial Qty", "no_purchases_available": "No purchases with available chickens.", "create_purchases_first": "You need to create purchases first.", "click_here_create_purchase": "Click here to create a purchase", "no_farms_available": "No farms available.", "create_farms_first": "You need to create farms first.", "click_here_create_farm": "Click here to create a farm", "expected_return_date": "Expected Return Date", "duration": "Duration", "days_text": "days", "investment": "Investment", "losses": "Losses", "revenue": "Revenue", "profit_breakdown": "Profit Breakdown", "total_profit": "Total Profit", "completion": "Completion", "no_process_data": "No process data available", "top_profitable_purchases": "Top Profitable Purchases", "loss_analysis": "Loss Analysis", "loading_process_report": "Loading process report...", "export_as_pdf": "Export as PDF", "export_as_csv": "Export as CSV", "export_as_excel": "Export as Excel", "export_as_json": "Export as JSON", "print_report": "Print Report", "formatted_pdf_document": "Formatted PDF document", "comma_separated_values": "Comma-separated values", "microsoft_excel_format": "Microsoft Excel format", "json_data_format": "JSON data format", "print_current_page": "Print current page", "filter_applied": "Filter Applied", "records": "records", "summary": "Summary", "total_records": "Total Records", "detailed_process_data": "Detailed Process Data", "generated_on": "Generated on", "at": "at", "filter": "Filter", "generated": "Generated"}, "profile": {"title": "Profile", "profile_page": "Profile Page", "personal_information": "Personal Information", "additional_information": "Additional Information", "change_password": "Change Password", "current_password": "Current Password", "new_password": "New Password", "confirm_new_password": "Confirm New Password", "update_password": "Update Password", "save_changes": "Save Changes", "bio": "Bio", "birth_date": "Birth Date", "join_date": "Join Date", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "profile_picture": "Profile Picture", "upload_image": "Upload Image", "validation": {"first_name_required": "First name is required", "last_name_required": "Last name is required", "email_required": "Email is required", "current_password_required": "Current password is required", "new_password_required": "New password is required", "password_min_length": "Password must be at least 6 characters", "passwords_not_match": "Passwords do not match", "invalid_email": "Invalid email format"}, "messages": {"profile_updated": "Profile updated successfully", "password_updated": "Password updated successfully", "error_updating_profile": "Error updating profile", "error_updating_password": "Error updating password", "image_too_large": "Image size should be less than 10MB", "invalid_image_type": "Please upload an image file"}}, "notifications": {"title": "Notifications", "notifications_page": "Notifications Page", "contact_notifications": "Contact Form Notifications", "mark_as_read": "<PERSON> <PERSON>", "mark_all_read": "<PERSON> as <PERSON>", "clear_all_contacts": "Clear All Contacts", "refresh": "Refresh", "no_notifications": "No notifications", "new_notification": "New Notification", "contact_submissions": "All Contact Submissions", "latest_first": "Latest First", "view_details": "View Details", "delete": "Delete", "contact_details": "Contact Details", "name": "Name", "email": "Email", "phone": "Phone", "subject": "Subject", "message": "Message", "submitted": "Submitted", "close": "Close", "delete_contact": "Delete Contact", "confirm_delete_contact": "Are you sure you want to delete this contact?", "confirm_clear_all": "Are you sure you want to clear all contact notifications and submissions?", "no_contact_submissions": "No contact form submissions yet", "messages": {"marked_as_read": "Marked as read", "all_marked_as_read": "All notifications marked as read", "contact_deleted": "Contact deleted successfully", "all_contacts_cleared": "All contacts cleared successfully", "error_loading_contacts": "Error loading contacts", "error_deleting_contact": "Error deleting contact"}}, "common": {"loading": "Loading...", "saving": "Saving...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "actions": "Actions", "status": "Status", "date": "Date", "name": "Name", "description": "Description", "quantity": "Quantity", "price": "Price", "total": "Total", "notes": "Notes", "archive": "Archive"}, "notFound": {"title": "Page Not Found", "description": "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.", "homeButton": "Go to Homepage", "contactButton": "Contact Support"}}