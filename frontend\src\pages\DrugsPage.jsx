'use client';

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Package,
  MapPin,
  DollarSign,
  Activity,
  TrendingUp,
  ArrowRight,
  BarChart3,
} from 'lucide-react';
import { useFeed } from '../contexts/FeedContext';
import { useLanguage } from '../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../components/feed-components/card';
import Button from '../components/Button';

const DrugsPage = () => {
  const navigate = useNavigate();
  const { fetchDrugPurchases } = useFeed();
  const { language, t } = useLanguage();
  
  const [purchaseStats, setPurchaseStats] = useState({
    totalPurchases: 0,
    totalQuantity: 0,
    totalAmount: 0,
  });
  
  const [transferStats, setTransferStats] = useState({
    totalTransfers: 0,
    totalQuantity: 0,
    completedTransfers: 0,
  });

  const [loading, setLoading] = useState(true);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    // Only load stats if function is available
    if (fetchDrugPurchases) {
      loadStats();
    }
  }, [fetchDrugPurchases]);

  const loadStats = async () => {
    try {
      setLoading(true);

      // Load purchase stats from API
      const purchaseResponse = await fetch('http://localhost:5432/api/v1/drug-purchases');
      if (purchaseResponse.ok) {
        const purchaseData = await purchaseResponse.json();
        if (purchaseData.success && Array.isArray(purchaseData.data)) {
          const purchases = purchaseData.data;
          const totalAmount = purchases.reduce((sum, p) => sum + (Number(p.total_amount) || 0), 0);
          setPurchaseStats({
            totalPurchases: purchases.length,
            totalQuantity: purchases.reduce((sum, p) => sum + (Number(p.quantity) || 0), 0),
            totalAmount: totalAmount,
          });
        } else {
          setPurchaseStats({
            totalPurchases: 0,
            totalQuantity: 0,
            totalAmount: 0,
          });
        }
      } else {
        setPurchaseStats({
          totalPurchases: 0,
          totalQuantity: 0,
          totalAmount: 0,
        });
      }

      // Load transfer stats
      const transferResponse = await fetch('http://localhost:5432/api/v1/drug-transfers');
      if (transferResponse.ok) {
        const transferData = await transferResponse.json();
        if (transferData.success && Array.isArray(transferData.data)) {
          const transfers = transferData.data;
          const totalTransferQuantity = transfers.reduce((sum, t) => {
            const quantity = Number(t.quantity) || 0;
            return sum + quantity;
          }, 0);

          const completedTransfers = transfers.filter(t => t.status === 'Completed').length;

          setTransferStats({
            totalTransfers: transfers.length || 0,
            totalQuantity: totalTransferQuantity,
            completedTransfers: completedTransfers,
          });
        } else {
          setTransferStats({
            totalTransfers: 0,
            totalQuantity: 0,
            completedTransfers: 0,
          });
        }
      } else {
        setTransferStats({
          totalTransfers: 0,
          totalQuantity: 0,
          completedTransfers: 0,
        });
      }
    
    } catch (error) {
      console.error('Error loading stats:', error);
      // Set default values on error
      setPurchaseStats({
        totalPurchases: 0,
        totalQuantity: 0,
        totalAmount: 0,
      });
      setTransferStats({
        totalTransfers: 0,
        totalQuantity: 0,
        completedTransfers: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('drugs.title')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {t('drugs.description')}
            </p>
          </div>
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => navigate('/admin/drugs/purchases')}
            >
              <Package className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('drugs.purchases_button')}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => navigate('/admin/drugs/transfers')}
            >
              <MapPin className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('drugs.transfers_button')}
            </Button>
            <Button onClick={() => navigate('/admin/drugs/purchases/add')} variant="primary" className="border border-orange-600">
              <Plus className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('drugs.purchase_drugs_button')}
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 border-l-4 border-l-purple-500" onClick={() => navigate('/admin/drugs/purchases')}>
            <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <div>
                <CardTitle className="text-lg font-semibold text-purple-700">{t('drugs.purchases.title')}</CardTitle>
                <p className="text-sm text-gray-500 mt-1">{t('drugs.purchases.description')}</p>
              </div>
              <Package className="h-8 w-8 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-purple-600">{t('drugs.manage')}</div>
                <ArrowRight className="h-5 w-5 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500" onClick={() => navigate('/admin/drugs/transfers')}>
            <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <div>
                <CardTitle className="text-lg font-semibold text-green-700">{t('drugs.transfers.title')}</CardTitle>
                <p className="text-sm text-gray-500 mt-1">{t('drugs.transfers.description')}</p>
              </div>
              <MapPin className="h-8 w-8 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-green-600">{t('drugs.manage')}</div>
                <ArrowRight className="h-5 w-5 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 border-l-4 border-l-orange-500" onClick={() => navigate('/admin/drugs/reports')}>
            <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <div>
                <CardTitle className="text-lg font-semibold text-orange-700">{t('drugs.reports.title')}</CardTitle>
                <p className="text-sm text-gray-500 mt-1">{t('drugs.reports.description')}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-orange-600">{t('drugs.analyze')}</div>
                <ArrowRight className="h-5 w-5 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Purchase Statistics */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{t('drugs.statistics.purchase_overview')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_purchases')}</CardTitle>
                <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{purchaseStats.totalPurchases}</div>
                <p className="text-xs text-muted-foreground">{t('drugs.statistics.purchase_orders')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_quantity')}</CardTitle>
                <Package className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{purchaseStats.totalQuantity}</div>
                <p className="text-xs text-muted-foreground">{t('drugs.statistics.units_purchased')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_amount')}</CardTitle>
                <DollarSign className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? '$0.00' : `$${(purchaseStats.totalAmount || 0).toFixed(2)}`}
                </div>
                <p className="text-xs text-muted-foreground">{t('drugs.statistics.total_spent')}</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Transfer Statistics */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{t('drugs.statistics.transfer_overview')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <CardTitle className="text-sm font-medium">{t('drugs.statistics.total_transfers')}</CardTitle>
                <TrendingUp className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{transferStats.totalTransfers}</div>
                <p className="text-xs text-muted-foreground">{t('drugs.statistics.transfer_orders')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <CardTitle className="text-sm font-medium">{t('drugs.statistics.transferred_quantity')}</CardTitle>
                <Package className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{transferStats.totalQuantity}</div>
                <p className="text-xs text-muted-foreground">{t('drugs.statistics.units_transferred')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <CardTitle className="text-sm font-medium">{t('drugs.statistics.completed')}</CardTitle>
                <MapPin className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{transferStats.completedTransfers}</div>
                <p className="text-xs text-muted-foreground">{t('drugs.statistics.successful_transfers')}</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Actions Footer */}
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.quick_actions')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={() => navigate('/admin/drugs/purchases/add')}
                className="w-full justify-start h-12"
                variant="outline"
              >
                <Plus className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
                {t('drugs.purchase_new_drugs')}
              </Button>
              <Button
                onClick={() => navigate('/admin/drugs/transfers/add')}
                className="w-full justify-start h-12"
                variant="outline"
              >
                <MapPin className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
                {t('drugs.transfer_to_farm')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DrugsPage;
